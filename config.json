{"labels": {"SPIDER_NET_VPC_NAME": "", "SPIDER_NET_SUBNET": "spidernet", "SPIDER_NET_IP_AUTO_ASSIGN": "true", "SPIDER_NET_IP": "", "SPIDER_NET_SECURITY_GROUP": "spidernetgroup-vpc23690050hn01", "SPIDER_NET_VPC": "vpc23690050hn01", "SPIDER_NET_SUBNET_NAME": ""}, "id": "/ids/server", "backoffFactor": 1.15, "backoffSeconds": 1, "container": {"portMappings": [], "type": "DOCKER", "volumes": [], "docker": {"parameters": [{"key": "volume", "value": "/var/lib/lxc/:/var/lib/lxc/:shared"}, {"key": "volume", "value": "/var/lib/lxc/lxcfs/proc/uptime:/proc/uptime"}, {"key": "volume", "value": "/var/lib/lxc/lxcfs/proc/swaps:/proc/swaps"}, {"key": "volume", "value": "/var/lib/lxc/lxcfs/proc/stat:/proc/stat"}, {"key": "volume", "value": "/var/lib/lxc/lxcfs/proc/diskstats:/proc/diskstats"}, {"key": "volume", "value": "/var/lib/lxc/lxcfs/proc/meminfo:/proc/meminfo"}, {"key": "volume", "value": "/var/lib/lxc/lxcfs/proc/cpuinfo:/proc/cpuinfo"}, {"key": "volume", "value": "/vdata/csi/log/384049441126-hn050-production/default/ids/server:/logs:rw"}], "image": "harbor.dcos.huhe.unicom.local/hi-apps/bonc-ids-server:v1.0", "forcePullImage": false, "privileged": false}}, "cpus": 0.1, "disk": 0, "instances": 1, "maxLaunchDelaySeconds": 3600, "mem": 8192, "gpus": 0, "networks": [{"name": "spidernetgroup-vpc23690050hn01", "mode": "container"}], "requirePorts": false, "upgradeStrategy": {"maximumOverCapacity": 0, "minimumHealthCapacity": 0}, "killSelection": "YOUNGEST_FIRST", "unreachableStrategy": {"inactiveAfterSeconds": 0, "expungeAfterSeconds": 0}, "healthChecks": [{"gracePeriodSeconds": 300, "intervalSeconds": 60, "maxConsecutiveFailures": 3, "timeoutSeconds": 20, "delaySeconds": 15, "protocol": "COMMAND", "command": {"value": "curl -f -X GET http://localhost:19001/ids-server/heart-beat"}}], "fetch": [], "constraints": [], "env": {}}