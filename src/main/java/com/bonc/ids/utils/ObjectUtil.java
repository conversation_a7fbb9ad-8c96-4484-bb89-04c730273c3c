package com.bonc.ids.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 常用object转换工具类
 * <AUTHOR>
 *
 */
public class ObjectUtil {
	/**
	 * 将对象转换为String
	 * @param o
	 * @return
	 */
	public static String toString(Object o) {
		if(o == null) {
			return "";
		}
		return String.valueOf(o);
	}
	
	/**
	 * 将对象转换为Long
	 * @param o
	 * @return
	 */
	public static Long toLong(Object o) {
		if(o == null) {
			return 0L;
		}
		String s = String.valueOf(o);
		if(StringUtils.isBlank(s)) {
			return 0L;
		}
		return Long.valueOf(s);
	}
	
	/**
	 * 将对象转换为Integer
	 * @param o
	 * @return
	 */
	public static Integer toInt(Object o) {
		if(o == null) {
			return 0;
		}
		String s = String.valueOf(o);
		if(StringUtils.isBlank(s)) {
			return 0;
		}
		return Integer.valueOf(s);
	}
	
	/**
	 * 将对象转换为Double
	 * @param o
	 * @return
	 */
	public static Double toDouble(Object o) {
		if(o == null || StringUtils.isBlank(o.toString())) {
			return 0.0;
		}
		if(o instanceof Integer) {
			return new Integer((int) o).doubleValue();
		}
		return Double.valueOf(o.toString());
	}
	
	/**
	 * 首字母大写
	 * @param s
	 * @return
	 */
	public static String firstLetterUpper(String s) {  
        byte[] items = s.getBytes();  
        items[0] = (byte) ((char) items[0] - 'a' + 'A');  
        return new String(items);  
    }
}
