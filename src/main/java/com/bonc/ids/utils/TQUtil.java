package com.bonc.ids.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-10-19 15:59
 * @description 天擎（能力平台）认证参数生成工具
 */
public class TQUtil {

    /**
     * 获取请求的头部信息
     * @return
     */
    public static Map<String, Object> getHead(String appId, String appSecret) {
        Date date = new Date();
        String timestamp = DateExtendUtil.format(date, "yyyy-MM-dd HH:mm:ss SSS");
        String transId = DateExtendUtil.format(date, "yyyyMMddHHmmssSSS") + RandomUtil.randomNumbers(6);
        StringBuilder builder = new StringBuilder();
        builder.append("APP_ID").append(appId)
                .append("TIMESTAMP").append(timestamp)
                .append("TRANS_ID").append(transId)
                .append(appSecret);
        String token = SecureUtil.md5(builder.toString());
        Map<String, Object> head = new HashMap<>();
        head.put("APP_ID", appId);
        head.put("TIMESTAMP", timestamp);
        head.put("TRANS_ID", transId);
        head.put("TOKEN", token);
        return head;
    }
}
