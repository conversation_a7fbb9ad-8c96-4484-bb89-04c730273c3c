package com.bonc.ids.utils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * ajax工具类，非controller中使用
 * <AUTHOR>
 *
 */
public class AjaxUtil {
	public static void response(Object o, HttpServletResponse response) {
		try {
			response.setCharacterEncoding("UTF-8");
			response.setContentType("application/json;charset=UTF-8");
//			PrintWriter out = response.getWriter();
//			out.print(JSONUtil.toJSONString(o));
//			out.close();
			ServletOutputStream outputStream = response.getOutputStream();
			outputStream.write(JSONExtendUtil.toJsonStr(o).getBytes());
			outputStream.flush();
			outputStream.close();
		} catch (IOException e) {
		}
	}
}
