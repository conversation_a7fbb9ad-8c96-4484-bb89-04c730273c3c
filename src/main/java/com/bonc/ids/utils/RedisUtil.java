package com.bonc.ids.utils;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2022-05-19 9:28
 * @description Redis工具类
 */
@Component
public class RedisUtil {
    private static RedisTemplate redisTemplate;

    public RedisUtil(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 读取数据
     * @param key
     * @return
     */
    public static <T> T get(String key) {
        ValueOperations<String, T> operations = redisTemplate.opsForValue();
        return operations.get(key);
    }

    /**
     * 写入数据（不过期）
     * @param key
     * @param value
     */
    public static <T> void set(String key, T value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 写入数据（可设置过期时间，单位：秒）
     * @param key
     * @param value
     * @param expire
     */
    public static <T> void set(String key, T value, Long expire) {
        redisTemplate.opsForValue().set(key, value, expire, TimeUnit.SECONDS);
    }

    /**
     * 是否存在对应的Key
     * @param key
     * @return
     */
    public static boolean exists(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 删除对应的Key
     * @param key
     * @return
     */
    public static boolean delete(String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 获取key的失效时间(单位秒)
     * @param key
     * @return
     */
    public static long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 批量删除对应的Key
     * @param keys
     */
    public static void deleteBatch(List<String> keys) {
        keys.forEach(key -> {
            redisTemplate.delete(key);
        });
    }
}
