package com.bonc.ids.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据处理工具类
 * <AUTHOR>
 *
 */
public class DataUtil {
	/**
	 * 将数据中的String类型的true或false转换为boolean类型
	 * @param list
	 * @return
	 */
	public static List<Map<String, Object>> string2Boolean(List<Map<String, Object>> list) {
		if(list == null) return null;
		List<Map<String, Object>> result = new ArrayList<>();
		int size = list.size();
		for(int i=0; i<size; i++) {
			Map<String, Object> m = list.get(i);
			for(String key : m.keySet()) {
				Object val = m.get(key);
				if(val instanceof String) {
					String newVal = (String) val;
					if("false".equalsIgnoreCase(newVal) || "true".equalsIgnoreCase(newVal)) {
						m.put(key, Boolean.valueOf(newVal));
					}
				}
			}
			result.add(m);
		}
		return result;
	}
	
	/**
	 * 获取List<Map>中的value，去除key
	 * @param list
	 * @param key
	 * @return
	 */
	public static List<String> getValueList(List<Map<String, Object>> list, String key) {
		if(list == null) return null;
		List<String> result = new ArrayList<>();
		int size = list.size();
		for(int i=0; i<size; i++) {
			Map<String, Object> m = list.get(i);
			for(String s : m.keySet()) {
				if(s.equals(key)) {
					Object val = m.get(key);
					if(val instanceof String) {
						result.add((String) val);
					} else {
                        result.add(String.valueOf(val));
                    }
				}
			}
		}
		return result;
	}
}
