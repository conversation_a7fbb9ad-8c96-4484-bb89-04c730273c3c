package com.bonc.ids.utils;

import com.bonc.ids.entity.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class HttpUtil {
    private static Environment environment;

    public HttpUtil(Environment environment) {
        HttpUtil.environment = environment;
    }

    /**
     * 生成当前访问的url
     * @return
     */
    public static String getUrl() {
        HttpServletRequest request = getRequest();
        String scheme = request.getHeader("X-Forwarded-Scheme");
        if(StringUtils.isBlank(scheme)) {
            scheme = "http";
        }
        String host = request.getServerName();
        int port = request.getServerPort();
        String contextPath = request.getContextPath();
        String url = scheme + "://" + host;
        if(port != 80) {
            url += (":" + port);
        }
        return url + contextPath;
    }
    /**
     * 生成http headers
     * @param filename
     * @return
     * @throws Exception
     */
    public static HttpHeaders getHeaders(String filename) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Content-Disposition", String.format("attachment; filename=\"%s\"", new String(filename.getBytes("UTF-8"), "ISO8859-1")));
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        headers.add("Access-Control-Expose-Headers", "Content-Disposition");
        return headers;
    }

    /**
     * 获取当前的request
     * @return
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        return request;
    }

    /**
     * 获取当前请求的response
     * @return
     */
    public static HttpServletResponse getResponse() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        return response;
    }

    /**
     * Post请求
     * @param url
     * @param params
     * @return
     */
    public static Object post(String url, Map<String, Object> params) {
        log.debug("Http Post请求地址：{}，参数：{}", url, params);

        RestTemplate template = new RestTemplate();
        Object result = template.postForObject(url, params, Object.class);

        return result;
    }

    /**
     * Get请求-带参数
     * @param url
     * @param params
     * @return
     */
    public static Object get(String url, Map<String, Object> params) {
        log.debug("Http Get请求地址：{}，参数：{}", url, params);

        RestTemplate template = new RestTemplate();
        Object result = template.getForObject(url, Object.class, params);

        return result;
    }

    /**
     * Get请求-无参数
     * @param url
     * @return
     */
    public static Object get(String url) {
        log.debug("Http Get请求地址：{}", url);

        RestTemplate template = new RestTemplate();
        Object result = template.getForObject(url, Object.class);

        return result;
    }

    /**
     * 根据token获取用户信息（联调集团经分）
     * @param token
     * @return
     */
    public static Map getUserInfo(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", token);
        HttpEntity entity = new HttpEntity(headers);
        RestTemplate template = new RestTemplate();
        String url = environment.getProperty("app.unicom-dss-api.user");
        ResponseEntity<Map> responseEntity = template.exchange(url, HttpMethod.GET, entity, Map.class);
        return responseEntity.getBody();
    }

    /**
     * 经分通用数据接口请求服务
     * @param id
     * @param data
     * @return
     */
    public static <T> ResultEntity<T> getDssCommonData(String id, Map<String, Object> data) {
        String appId = environment.getProperty("app.ability-platform.app-id");
        String appSecret = environment.getProperty("app.ability-platform.app-secret");
        String url = environment.getProperty("app.ability-platform.dss-common-api");
        Map<String, Object> head = TQUtil.getHead(appId, appSecret);
        Map<String, Object> body = new HashMap<>();
        Map<String, Object> req = new HashMap<>();
        req.put("id", id);
        req.put("data", data);
        body.put("DSS_COMMON_DATA_REQ", req);
        Map<String, Object> params = new HashMap<>();
        params.put("UNI_BSS_HEAD", head);
        params.put("UNI_BSS_BODY", body);
        Object result = post(url, params);
        log.debug("接口返回结果：{}", result);
        //解析返回信息
        String code = JSONExtendUtil.getValueByKey(result, "$.UNI_BSS_HEAD.RESP_CODE");
        String msg = JSONExtendUtil.getValueByKey(result, "$.UNI_BSS_HEAD.RESP_DESC");
        //判断head信息是否正确
        if (StringUtils.isEmpty(code) || !"00000".equalsIgnoreCase(code)) {
            return ResultEntity.error(msg);
        }
        //判断body信息是否正确
        int code1 = JSONExtendUtil.getValueByKey(result, "$.UNI_BSS_BODY.DSS_COMMON_DATA_RSP.code");
        msg = JSONExtendUtil.getValueByKey(result, "$.UNI_BSS_BODY.DSS_COMMON_DATA_RSP.msg");
        if (code1 != 200) {
            return ResultEntity.error(msg);
        }
        T o = JSONExtendUtil.getValueByKey(result, "$.UNI_BSS_BODY.DSS_COMMON_DATA_RSP.data");
        return ResultEntity.success("请求成功", o);
    }
}
