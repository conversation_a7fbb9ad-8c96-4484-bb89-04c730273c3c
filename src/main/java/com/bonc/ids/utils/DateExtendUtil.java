package com.bonc.ids.utils;


import cn.hutool.core.date.DateUtil;
import com.bonc.ids.dao.DaoHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;


/**
 * 日期工具类
 * <AUTHOR>
 *
 */
@Slf4j
public class DateExtendUtil extends DateUtil {

	/**
	 * 昨日
	 * @param fmt
	 * @return
	 */
	public static String yesterday(String fmt) {
		return DateExtendUtil.format(DateExtendUtil.yesterday(), fmt);
	}

	/**
	 * 获取数据表最大可用账期
	 * @param daoHelper
	 * @param tableName
	 * @return
	 */
	public static String getMaxCycle(Da<PERSON><PERSON><PERSON><PERSON> daoHelper, String tableName) {
		String date = daoHelper.queryForObject("mapper.common.getMaxCycle", tableName);
		return date;
	}

	public static void setOrgDate(<PERSON><PERSON><PERSON><PERSON>per daoHelper, Map<String, Object> params){
		//获取组织最大账期

		String orgDate = getMaxCycle(da<PERSON><PERSON><PERSON><PERSON>,"DM.ORG_ORGANIZATION_MOD_ACCT");
		if (!StringUtils.isEmpty(orgDate)) {
			orgDate = orgDate.substring(0, 8);
		} else {
			orgDate = yesterday("yyyyMMdd");
		}
		params.put("orgDate", orgDate);
	}
}
