package com.bonc.ids.utils;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.PageHelper;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-08-11 10:32
 * @discription 构造分页参数
 */
public class PageUtil {

    public static void startPage(Map<String, Object> params) {
        int pageSize = ConvertUtil.toInt(params.get("pageSize"));
        int pageNum = ConvertUtil.toInt(params.get("current"));
        if (pageSize == 0) pageSize = 10;
        if (pageNum == 0) pageNum = 1;
        Map<String, Integer> result = new HashMap<>();
        result.put("pageSize", pageSize);
        result.put("pageNum", pageNum);
        PageHelper.startPage(result);
    }
}
