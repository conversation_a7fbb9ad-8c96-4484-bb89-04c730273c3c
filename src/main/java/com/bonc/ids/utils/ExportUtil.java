package com.bonc.ids.utils;

import cn.hutool.core.util.RandomUtil;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.ResultEntity;
import com.github.liaochong.myexcel.utils.AttachmentExportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 导出工具类
 */
@Slf4j
@Component
public class ExportUtil {
    private static boolean encrypt0;
    private static boolean enable0;
    private static int passwordLength0;
    private static DaoHelper daoHelper0;
    private DaoHelper daoHelper;

    //是否启用导出
    @Value("${app.export.enable:false}")
    private Boolean enable;
    //是否加密（来源配置文件，默认不加密）
    @Value("${app.export.encrypt:false}")
    private Boolean encrypt;
    //密码长度（默认：6位）
    @Value("${app.export.encrypt-password-length:6}")
    private Integer passwordLength;

    public ExportUtil(DaoHelper daoHelper) {
        this.daoHelper = daoHelper;
    }

    @PostConstruct
    private void init() {
        daoHelper0 = this.daoHelper;
        encrypt0 = this.encrypt;
        enable0 = this.enable;
        passwordLength0 = this.passwordLength;
    }

    /**
     * 导出为附件（是否加密跟随系统）
     *
     * @param workbook
     * @param fileName
     */
    public static void attachment(Map<String, Object> params, Workbook workbook, String fileName) {
        attachment(params, workbook, fileName, encrypt0);
    }

    /**
     * 导出为附件（可单独设置是否加密）
     *
     * @param workbook
     * @param fileName
     * @param encrypt
     */
    public static void attachment(Map<String, Object> params, Workbook workbook, String fileName, boolean encrypt) {
        String profile = SpringContextUtil.getActiveProfile();
        HttpServletResponse response = HttpUtil.getResponse();
        //生产环境才判断是否启用
        if (!enable0 && "prod".equalsIgnoreCase(profile)) {
            log.debug("系统未启用导出功能");
            AjaxUtil.response(ResultEntity.error("系统未启用导出功能"), response);
            return;
        }
        if (encrypt && "prod".equalsIgnoreCase(profile)) {
            //生成环境才启用加密
            String password = RandomUtil.randomString(passwordLength0);
            AttachmentExportUtil.encryptExport(workbook, fileName, response, password);
            if (params == null) params = new HashMap<>();
            params.put("password", password);
            sendPassword(params);
        } else {
            AttachmentExportUtil.export(workbook, fileName, response);
        }
        if (!"prod".equalsIgnoreCase(profile)) return;
        //记录导出日志
        try {
            params.put("count", workbook.getSheetAt(0).getPhysicalNumberOfRows() - 1);
            params.put("id", UUID.randomUUID().toString());
            log.error("导出参数：{}", params);
            daoHelper0.insert("mapper.common.saveExportLog", params);
        } catch (Exception e) {
            log.error("导出日志记录出错：{}", e.getMessage());
        }
    }

    /**
     * 通过短信发送加密的密码
     *
     * @param params
     */
    private static void sendPassword(Map<String, Object> params) {
        String profile = SpringContextUtil.getActiveProfile();
        //非生产模式发送号码固定为15595912018
        String phone = "1559591208";
        if ("prod".equals(profile)) {
            phone = daoHelper0.queryForObject("mapper.common.getPhoneNumber", params);
        }
        if (StringUtils.isEmpty(phone)) return;
        params.put("phone", phone);
        daoHelper0.insert("mapper.common.sendExtractPassword", params);
    }
}
