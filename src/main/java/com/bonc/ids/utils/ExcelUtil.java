package com.bonc.ids.utils;

import cn.hutool.core.convert.Convert;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * excel操作工具类
 * <AUTHOR>
 *
 */
public class ExcelUtil {
    private final static Logger LOG = LoggerFactory.getLogger(ExcelUtil.class);
    private static final String EXCEL_XLS = "xls";
    private static final String EXCEL_XLSX = "xlsx";
    /**
     * 返回excel对象，用于controller
     * @param fileName
     * @param cols Map格式字段名与中文名称映射关系<br>例如: {"id":1,"name":"姓名",...}
     * @param rows List格式数据，不包含表头，格式：[{"id":1,"name":"test1"},{"id":2,"name":"test2"},{"id":3,"name":"test3"},...]
     * @return
     */
    public static Map<String, Object> data(String fileName, Map<String, String> cols, List<Map<String, Object>> rows) {
        if(cols.isEmpty() || cols.size() == 0) throw new NullPointerException("字段名称Map不能为空");
        Map<String, Object> m = new HashMap<>();
        String newFileName = getFileName(fileName);
        m.put("fileName", newFileName);
        m.put("cols", cols);
        m.put("rows", rows);
        return m;
    }

    /**
     * 处理文件名，默认处理为2007
     * @param fileName
     * @return
     */
    public static String getFileName(String fileName) {
        //默认文件名
        if(StringUtils.isBlank(fileName)) fileName = "export_excel.xlsx";
        //检查是否包含扩展名，不包含则添加
        if(fileName.lastIndexOf(".") == -1) fileName += ".xlsx";
        //检查扩展名是否正确
        int dotIndex = fileName.lastIndexOf(".");
        if(dotIndex != -1) {
            String ext = fileName.substring(dotIndex);
            if(!".xls".equalsIgnoreCase(ext) || !".xlsx".equalsIgnoreCase(ext)) {
                fileName = fileName.substring(0, dotIndex) + ".xlsx";
            }
        }
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fileName;
    }

    /**
     * 判断文件格式是否为2007及以上
     * @param fileName
     * @return
     */
    public static boolean isXlsx(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        String ext = fileName.substring(dotIndex);
        if("xlsx".equalsIgnoreCase(ext)) return true;
        return false;
    }

    /**
     * 判断文件格式是否为2003
     * @param fileName
     * @return
     */
    public static boolean isXls(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        String ext = fileName.substring(dotIndex);
        if("xls".equalsIgnoreCase(ext)) return true;
        return false;
    }

    /**
     * 将上传的Excel文件转换为List
     * @param file
     * @param columns 字段
     * @return
     */
    public static Map<String, Object> file2List(MultipartFile file, List<String> columns,int num) {
        LOG.debug("将上传的Excel文件转换为List数据, 文件名：{}，字段：{}", file.getName(), columns,num);
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Map<String, Object> bookResult = validExcel(workbook);
            int code = ConvertUtil.toInt(bookResult.get("code"));
            if (code == 1) {
                LOG.error("文件转换为List出错：{}", bookResult);
                result.putAll(bookResult);
                return result;
            }
            //int sheets = workbook.getNumberOfSheets();//这里是读取多个sheet表格，一般上传功能，只用到第一个sheet页面，先强制读取第一个，以后有类似的在加参数
            int sheets =1;
            for (int i = 0; i < sheets; i++) {
                Sheet sheet = workbook.getSheetAt(i);
                int rows = sheet.getPhysicalNumberOfRows();
                // 从第二行开始，默认第一行为表头 通用num 传1 ,特殊的可以自定义
                for (int j = num; j < rows; j++) {
                    Map<String, Object> rowMap = new HashMap<>();
                    Row row = sheet.getRow(j);
                    for (int l = 0; l < columns.size(); l++) {
                        String column = columns.get(l);
                        LOG.debug("----------------------------");
                        LOG.debug("字段名称：{}", column);
                        Cell cell = row.getCell(l);
                        Object value = getCellValue(cell);
                        LOG.debug("单元格值：{}", value);
                        rowMap.put(column, value);
                    }
                    list.add(rowMap);
                }
            }
            //这里就不打印了，影响上传的效率
            //LOG.debug("转换成功后的数据：{}", list);
            result.put("code", 0);
            result.put("message", "文件解析成功");
        } catch (IOException e) {
            LOG.error("转换IO异常：{}", e.getMessage());
            result.put("code", 1);
            result.put("message", "文件读取异常");
        }
        result.put("list", list);
        result.put("count", list.size());
        return result;
    }
    /**
     * 将上传的Excel文件转换为List
     * @param file 接收File类型
     * @param columns 字段
     * @return
     */
    public static Map<String, Object> file3List(File file, List<String> columns,int num) {
        LOG.debug("将上传的Excel文件转换为List数据, 文件名：{}，字段：{}", file.getName(), columns,num);
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            Workbook workbook = WorkbookFactory.create(file);
            Map<String, Object> bookResult = validExcel(workbook);
            int code = ConvertUtil.toInt(bookResult.get("code"));
            if (code == 1) {
                LOG.error("文件转换为List出错：{}", bookResult);
                result.putAll(bookResult);
                return result;
            }
            //int sheets = workbook.getNumberOfSheets();//这里是读取多个sheet表格，一般上传功能，只用到第一个sheet页面，先强制读取第一个，以后有类似的在加参数
            int sheets =1;
            for (int i = 0; i < sheets; i++) {
                Sheet sheet = workbook.getSheetAt(i);
                int rows = sheet.getPhysicalNumberOfRows();
                // 从第二行开始，默认第一行为表头 通用num 传1 ,特殊的可以自定义
                for (int j = num; j < rows; j++) {
                    Map<String, Object> rowMap = new HashMap<>();
                    Row row = sheet.getRow(j);
                    for (int l = 0; l < columns.size(); l++) {
                        String column = columns.get(l);
                        LOG.debug("----------------------------");
                        LOG.debug("字段名称：{}", column);
                        Cell cell = row.getCell(l);
                        Object value = getCellValue(cell);
                        LOG.debug("单元格值：{}", value);
                        rowMap.put(column, value);
                    }
                    list.add(rowMap);
                }
            }
            //这里就不打印了，影响上传的效率
            //LOG.debug("转换成功后的数据：{}", list);
            result.put("code", 0);
            result.put("message", "文件解析成功");
        } catch (IOException e) {
            LOG.error("转换IO异常：{}", e.getMessage());
            result.put("code", 1);
            result.put("message", "文件读取异常");
        }
        result.put("list", list);
        result.put("count", list.size());
        return result;
    }
    /**
     * 将上传的Excel文件转换为List
     * @param file
     * @param columns 字段
     * @return
     */
    public static Map<String, Object> file3List(MultipartFile file, List<String> columns,int num,int rownum) {
        LOG.debug("将上传的Excel文件转换为List数据, 文件名：{}，字段：{}", file.getName(), columns,num);
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Map<String, Object> bookResult = validExcel(workbook);
            int code = ConvertUtil.toInt(bookResult.get("code"));
            if (code == 1) {
                LOG.error("文件转换为List出错：{}", bookResult);
                result.putAll(bookResult);
                return result;
            }
            //int sheets = workbook.getNumberOfSheets();//这里是读取多个sheet表格，一般上传功能，只用到第一个sheet页面，先强制读取第一个，以后有类似的在加参数
            int sheets =1;
            for (int i = 0; i < sheets; i++) {
                Sheet sheet = workbook.getSheetAt(i);
                int rows = rownum;
                // 从第二行开始，默认第一行为表头 通用num 传1 ,特殊的可以自定义
                for (int j = num; j < rows; j++) {
                    Map<String, Object> rowMap = new HashMap<>();
                    Row row = sheet.getRow(j);
                    for (int l = 0; l < columns.size(); l++) {
                        String column = columns.get(l);
                        LOG.debug("----------------------------");
                        LOG.debug("字段名称：{}", column);
                        Cell cell = row.getCell(l);
                        Object value = getCellValue(cell);
                        LOG.debug("单元格值：{}", value);
                        rowMap.put(column, value);
                    }
                    list.add(rowMap);
                }
            }
            //这里就不打印了，影响上传的效率
            //LOG.debug("转换成功后的数据：{}", list);
            result.put("code", 0);
            result.put("message", "文件解析成功");
        } catch (IOException e) {
            LOG.error("转换IO异常：{}", e.getMessage());
            result.put("code", 1);
            result.put("message", "文件读取异常");
        }
        result.put("list", list);
        return result;
    }

    /**
     * 校验Excel
     * @param workbook
     * @return
     */
    public static Map<String, Object> validExcel(Workbook workbook) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("message", "校验通过");
        int sheets = workbook.getNumberOfSheets();
        for(int i = 0; i < sheets; i++) {
            Sheet sheet = workbook.getSheetAt(i);
            Map<String, Object> sheetResult = validSheet(sheet);
            int code = ConvertUtil.toInt(sheetResult.get("code"));
            if (code == 1) {
                result.put("code", 1);
                result.put("message", "第" + (i + 1) + "个sheet数据为空");
                break;
            }
        }
        return result;
    }

    /**
     * 校验Sheet
     * @param sheet
     * @return
     */
    public static Map<String, Object> validSheet(Sheet sheet) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("message", "校验通过");
        int rows = sheet.getPhysicalNumberOfRows();
        if (rows < 2) {
            result.put("code", 1);
            result.put("message", "当前sheet数据为空");
        }
        return result;
    }

    /**
     * 获取单元格值
     * @param cell
     * @return
     */
    public static Object getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        try {
            if (cell.getCellType() == CellType.STRING) {//字符串
                return cell.getRichStringCellValue().getString();
            } else if(cell.getCellType() == CellType.FORMULA){//公式
                return cell.getNumericCellValue();
            }else if (cell.getCellType() == CellType.NUMERIC) {//数字
                if(DateUtil.isCellDateFormatted(cell)){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    return sdf.format(DateUtil.getJavaDate(cell.getNumericCellValue()));
                }
                return cell.getNumericCellValue();
            } else if (cell.getCellType() == CellType.BLANK) {//空白的设置为0
                return "";
            } else {
                return "";
            }
        } catch (Exception e) {
            LOG.error("获取单元格值出错：{}", e.getMessage());
        }
        return "";
    }

    //判断版本获取Wordboook
    public static Workbook getWorkbook(InputStream in, String fileName) throws IOException {
        Workbook wbook = null;
        if (fileName.endsWith(EXCEL_XLS)) {
            wbook = new HSSFWorkbook(in);
        } else if (fileName.endsWith(EXCEL_XLSX)) {
            wbook = new XSSFWorkbook(in);
        }
        return wbook;
    }

}
