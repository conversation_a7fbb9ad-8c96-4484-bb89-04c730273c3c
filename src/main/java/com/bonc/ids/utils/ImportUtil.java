package com.bonc.ids.utils;

import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.exceptions.FileTypeException;
import com.github.liaochong.myexcel.core.SaxExcelReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @create 2023-09-05 10:31
 * @description 数据导入工具类
 */
@Slf4j
@Component
public class ImportUtil {

    private static DaoHelper daoHelper;

    public ImportUtil(DaoHelper daoHelper) {
        ImportUtil.daoHelper = daoHelper;
    }
    
    /**
     * 导入Excel
     * @param file 文件对象
     * @param clazz 数据Bean
     * @param filterRows 需要过滤的行，从0开始
     * @return
     * @throws IOException
     */
    public static <T> List<T> excel(MultipartFile file, Class<T> clazz, int filterRows, Map<String, Object> params) throws IOException {
        return excelOrCsv(file, clazz, filterRows, null, ',', params, true);
    }

    /**
     * 导入Excel（默认过滤第一行）
     * @param file 文件对象
     * @param clazz 数据Bean
     * @return
     * @throws IOException
     */
    public static <T> List<T> excel(MultipartFile file, Class<T> clazz, Map<String, Object> params) throws IOException {
        return excel(file, clazz, 0, params, true);
    }

    /**
     * 导入CSV
     * @param file 文件对象
     * @param clazz 数据Bean
     * @return
     * @throws IOException
     */
    public static <T> List<T> csv(MultipartFile file, Class<T> clazz, Map<String, Object> params) throws IOException {
        return excelOrCsv(file, clazz, -1, "UTF-8", ',', params, true);
    }

    /**
     * 导入Excel
     * @param file 文件对象
     * @param clazz 数据Bean
     * @param filterRows 需要过滤的行，从0开始
     * @return
     * @throws IOException
     */
    public static <T> List<T> excel(MultipartFile file, Class<T> clazz, int filterRows, Map<String, Object> params, Boolean doLog) throws IOException {
        return excelOrCsv(file, clazz, filterRows, null, ',', params, doLog);
    }

    /**
     * 导入Excel（默认过滤第一行）
     * @param file 文件对象
     * @param clazz 数据Bean
     * @return
     * @throws IOException
     */
    public static <T> List<T> excel(MultipartFile file, Class<T> clazz, Map<String, Object> params, Boolean doLog) throws IOException {
        return excel(file, clazz, 0, params, doLog);
    }

    /**
     * 导入CSV
     * @param file 文件对象
     * @param clazz 数据Bean
     * @return
     * @throws IOException
     */
    public static <T> List<T> csv(MultipartFile file, Class<T> clazz, Map<String, Object> params, Boolean doLog) throws IOException {
        return excelOrCsv(file, clazz, -1, "UTF-8", ',', params, doLog);
    }

    /**
     * 导入Excel或者CSV文件
     * @param file
     * @param clazz
     * @param filterRows
     * @param charset
     * @param delimiter
     * @return
     * @throws IOException
     */
    public static <T> List<T> excelOrCsv(MultipartFile file, Class<T> clazz, int filterRows, String charset, char delimiter, Map<String, Object> params, Boolean doLog) throws IOException {
        if (file == null) {
            throw new NullPointerException("文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        log.debug("文件名：{}", fileName);
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
        log.debug("文件扩展名：{}", extension);
        if (StringUtils.isEmpty(extension) || !Arrays.asList("xls", "xlsx", "csv").contains(extension)) {
            throw new FileTypeException("只支持xls、xlsx、csv类型文件");
        }
        List<T> list = SaxExcelReader
                .of(clazz).rowFilter(row -> row.getRowNum() > filterRows)
                .csvCharset(charset)
                .csvDelimiter(delimiter)
                .ignoreBlankRow()
                .read(file.getInputStream());
        params.put("fileSize", file.getSize() / 1024);
        if (doLog) {        	
        	logger(list, fileName, params);
        }
        return list;
    }

    /**
     * 记录导入日志
     * @param list
     * @param filename
     * @param params
     */
    public static void logger(List<?> list, String filename, Map<String, Object> params) {
        Thread t = new Thread(() -> {
            try {
                params.put("id", UUID.randomUUID().toString());
                params.put("filename", filename);
                params.put("dataCount", list.size());
                daoHelper.insert("mapper.common.saveImportLog", params);
            } catch (Exception e) {
                log.error("数据导入日志记录出错，导入文件名：{}， 额外参数：{}， 错误信息：{}", filename, params, e.getMessage());
            }
        });
        t.start();
    }
}
