package com.bonc.ids.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @create 2020-08-17 11:53
 * @discription 正则工具
 */
public class RegexUtil {
    /**
     * 正则替换
     * @param regex
     * @param source
     * @param replacement
     * @param caseSensitive
     * @return
     */
    public static String replace(String source, String regex, String replacement, boolean caseSensitive) {
        StringBuffer buffer = new StringBuffer();
        Pattern pattern = Pattern.compile(regex, caseSensitive ? Pattern.CASE_INSENSITIVE : 0);
        Matcher matcher = pattern.matcher(source);
        while (matcher.find()) {
            matcher.appendReplacement(buffer, replacement);
        }
        matcher.appendTail(buffer);
        return buffer.toString();
    }

    /**
     * 正则替换(大小写不敏感)
     * @param source
     * @param regex
     * @param replacement
     * @return
     */
    public static String replace(String source, String regex, String replacement) {
        return replace(source, regex, replacement, false);
    }

    /**
     * 正则匹配
     * @param source
     * @param regex
     * @param caseSensitive
     * @return
     */
    public static boolean like(String source, String regex, boolean caseSensitive) {
        Pattern pattern = Pattern.compile(regex, caseSensitive ? Pattern.CASE_INSENSITIVE : 0);
        Matcher matcher = pattern.matcher(source);
        return matcher.matches();
    }

    /**
     * 正则匹配(大小写不敏感)
     * @param source
     * @param regex
     * @return
     */
    public static boolean like(String source, String regex) {
        return like(source, regex, false);
    }
}
