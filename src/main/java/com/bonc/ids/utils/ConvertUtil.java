package com.bonc.ids.utils;

import cn.hutool.core.convert.Convert;

/**
 * <AUTHOR>
 * @create 2024-01-12 9:20
 * @description
 */
public class ConvertUtil {

    /**
     * 转为整型
     * @param value
     * @return
     */
    public static int toInt(Object value) {
        if (value == null) {
            return 0;
        }
        Integer integerValue = Convert.toInt(value);
        if (integerValue == null) {
            return 0;
        }
        return integerValue.intValue();
    }

    /**
     * 转为long
     * @param value
     * @return
     */
    public static long toLong(Object value) {
        if (value == null) {
            return 0L;
        }
        Long longValue = Convert.toLong(value);
        if (longValue == null) {
            return 0L;
        }
        return longValue.longValue();
    }
}
