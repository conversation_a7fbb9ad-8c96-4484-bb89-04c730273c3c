package com.bonc.ids.utils;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.ResultCode;
import com.bonc.ids.entity.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.net.URL;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2023-01-09 10:10
 * @description 联通云 - 对象存储工具类
 */
@Slf4j
@Component
public class COSUtil {
    private static final String DEFAULT_BUCKET = "hi-ids-files";
    private static DaoHelper daoHelper;
    //COS访问key
    @Value("${app.cos-key}")
    private String accessKey;
    //COS访问密钥
    @Value("${app.cos-secret}")
    private String secretKey;
    //实例访问地址
    @Value("${app.cos-endpoint}")
    private String endpoint;
    //实例区域
    @Value("${app.cos-region}")
    private String region;

    static AWSCredentials credentials;
    static AWSStaticCredentialsProvider awsStaticCredentialsProvider;
    static ClientConfiguration config = new ClientConfiguration();;
    static EndpointConfiguration endpointConfiguration;
    static AmazonS3 conn;

    public COSUtil(DaoHelper daoHelper) {
        COSUtil.daoHelper = daoHelper;
    }

    @PostConstruct
    public void initConfig() {
        credentials = new BasicAWSCredentials(accessKey, secretKey);
        awsStaticCredentialsProvider = new AWSStaticCredentialsProvider(credentials);
        endpointConfiguration = new EndpointConfiguration(endpoint, region);
        conn = AmazonS3ClientBuilder.standard()
                .withCredentials(awsStaticCredentialsProvider)
                .withClientConfiguration(config.withProtocol(Protocol.HTTP).withSignerOverride("S3SignerType"))
                .withEndpointConfiguration(endpointConfiguration).build();
    }

    /**
     * 批量上传（使用默认存储桶）
     * @param files
     * @return
     */
    public static List<Map<String, Object>> uploadBatch(MultipartFile[] files) {
        return uploadBatch(DEFAULT_BUCKET, files);
    }

    /**
     * 批量上传
     * @param bucketName 存储桶名称
     * @param files 文件
     * @return
     */
    public static List<Map<String, Object>> uploadBatch(String bucketName, MultipartFile[] files) {
        List<Map<String, Object>> attachments = new ArrayList<>();
        List<ResultEntity> entities = new ArrayList<>();
        for (int i = 0; i < files.length; i++) {
            ResultEntity entity = upload(bucketName, files[i]);
            if (entity.getCode() == ResultCode.SUCCESS.code) {
                entities.add(entity);
            }
        }
        if (entities.size() != 0) {
            entities.forEach(entity -> {
                Map<String, Object> attachment = (Map<String, Object>) entity.getData();
                attachments.add(attachment);
            });
        }
        return attachments;
    }

    /**
     * 单个上传
     * @param bucketName 存储桶名称
     * @param file 文件
     * @return
     */
    public static ResultEntity upload(String bucketName, MultipartFile file) {
        //先判断存储桶是否存在，如果不存在则创建
        if (!conn.doesBucketExistV2(bucketName)) {
            conn.createBucket(bucketName);
        }
        //文件ID
        String fileId = UUID.randomUUID().toString();
        //原始文件名
        String originalName = file.getOriginalFilename();
        //文件扩展名
        String extension = originalName.substring(originalName.lastIndexOf("."));
        TransferManager tm = TransferManagerBuilder.standard().withS3Client(conn).build();
        //文件上传成功后的文件名称，为了防止覆盖，都以UUID进行存储
        String newFileName = UUID.randomUUID().toString() + extension;
        try {
            Upload upload = tm.upload(bucketName, newFileName, file.getInputStream(), new ObjectMetadata());
            upload.waitForCompletion();
            //将数据存储到数据库
            Map<String, Object> params = new HashMap<>();
            params.put("fileName", newFileName);
            params.put("originalName", originalName);
            params.put("fileExtension", extension);
            params.put("filePath", "/common/attachment?fileName=" + newFileName + "&originalName=" + originalName + "&bucket=" + bucketName);
            params.put("bucketName", bucketName);
            params.put("id", fileId);
            log.debug("上传成功：{}", params);
            //存储数据库
            daoHelper.insert("mapper.common.saveAttachment", params);
            return ResultEntity.success("上传成功", params);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("COS文件上传出错：{}", e.getMessage());
            return ResultEntity.error("文件上传出错");
        }
    }

    /**
     * 单个上传（使用默认存储桶）
     * @param file
     * @return
     */
    public static ResultEntity upload(MultipartFile file) {
        return upload(DEFAULT_BUCKET, file);
    }

    /**
     * 获取文件访问地址
     * @param bucketName
     * @param fileName
     * @return
     */
    public static ResultEntity getURL(String bucketName, String fileName) {
        if (!conn.doesBucketExistV2(bucketName)) {
            return ResultEntity.error("存储桶不存在");
        }
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, fileName);
        //生成临时链接，有效期1小时
        URL url = conn.generatePresignedUrl(request);
        String contentType = getContentType(bucketName, fileName);
        Map<String, Object> result = new HashMap<>();
        result.put("url", url.toString());
        result.put("contentType", contentType);
        return ResultEntity.success("生成成功", result);
    }

    /**
     * 获取文件访问地址（使用默认存储桶）
     * @param fileName
     * @return
     */
    public static ResultEntity getURL(String fileName) {
        return getURL(DEFAULT_BUCKET, fileName);
    }

    /**
     * 获取文件ContentType
     * @param bucketName
     * @param fileName
     * @return
     */
    public static String getContentType(String bucketName, String fileName) {
        ObjectMetadata metadata = conn.getObjectMetadata(bucketName, fileName);
        return metadata.getContentType();
    }

    /**
     * 获取文件ContentType（使用默认存储桶）
     * @param fileName
     * @return
     */
    public static String getContentType(String fileName) {
        return getContentType(fileName);
    }
}
