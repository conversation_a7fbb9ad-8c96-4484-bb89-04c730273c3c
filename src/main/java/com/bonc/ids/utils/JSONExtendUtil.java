package com.bonc.ids.utils;

import cn.hutool.json.JSONUtil;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;

/**
 * JSON工具类
 * <AUTHOR>
 *
 */
@Slf4j
public class JSONExtendUtil extends JSONUtil {
	/**
	 * 提取JSON中指定key的值
	 * @param json
	 * @param key
	 * @param <T>
	 * @return
	 */
	public static <T> T getValueByKey(Object json, String key) {
		try {
			return JsonPath.read(json, key);
		} catch (Exception e) {
			log.error("JSON提取出错, Key={}, 错误信息：{}", key, e.getMessage());
			return null;
		}
	}
}
