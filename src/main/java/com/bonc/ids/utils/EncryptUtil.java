package com.bonc.ids.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.codec.Base64;
import org.apache.shiro.codec.Hex;
import org.apache.shiro.crypto.AesCipherService;
import org.apache.shiro.crypto.hash.*;

import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;

/**
 * 加密工具类
 * <AUTHOR>
 *
 */
@Slf4j
public class EncryptUtil {
	
	/**
	 * base64加密
	 * @param s 需加密的字符串
	 * @return
	 */
	public static String encryptBase64(String s) {
		if(s == null) {
			return null;
		}
		try {
			return Base64.encodeToString(s.getBytes("UTF-8"));
		} catch (UnsupportedEncodingException e) {
			return s;
		}
	}
	
	/**
	 * base64解码
	 * @param s
	 * @return
	 */
	public static String decryptBase64(String s) {
		if(s == null) {
			return null;
		}
		return Base64.decodeToString(s.getBytes());
	}
	
	/**
	 * md5加密
	 * @param s 需加密的字符串
	 * @param salt 加密的salt
	 * @return
	 */
	public static String md5(String s, String salt) {
		if(s == null) {
			return null;
		}
		return new Md5Hash(s, salt).toString();
	}
	
	/**
	 * md5加密
	 * @param s 需加密的字符串
	 * @return
	 */
	public static String md5(String s) {
		if(s == null) {
			return null;
		}
		return new Md5Hash(s).toString();
	}
	
	/**
	 * sha1加密
	 * @param s 需加密的字符串
	 * @param salt 加密的salt
	 * @return
	 */
	public static String sha1(String s, String salt) {
		if(s == null) {
			return null;
		}
		return new Sha1Hash(s, salt).toString();
	}
	
	/**
	 * sha1加密
	 * @param s 需加密的字符串
	 * @return
	 */
	public static String sha1(String s) {
		if(s == null) {
			return null;
		}
		return new Sha1Hash(s).toString();
	}
	
	/**
	 * sha256加密
	 * @param s 需加密的字符串
	 * @param salt 加密的salt
	 * @return
	 */
	public static String sha256(String s, String salt) {
		if(s == null) {
			return null;
		}
		return new Sha256Hash(s, salt).toString();
	}
	
	/**
	 * sha256加密
	 * @param s 需加密的字符串
	 * @return
	 */
	public static String sha256(String s) {
		if(s == null) {
			return null;
		}
		return new Sha256Hash(s).toString();
	}
	
	/**
	 * sha384加密
	 * @param s 需加密的字符串
	 * @param salt 加密的salt
	 * @return
	 */
	public static String sha384(String s, String salt) {
		if(s == null) {
			return null;
		}
		return new Sha384Hash(s, salt).toString();
	}
	
	/**
	 * sha384加密
	 * @param s 需加密的字符串
	 * @return
	 */
	public static String sha384(String s) {
		if(s == null) {
			return null;
		}
		return new Sha384Hash(s).toString();
	}
	
	/**
	 * sha512加密
	 * @param s 需加密的字符串
	 * @param salt 加密的salt
	 * @return
	 */
	public static String sha512(String s, String salt) {
		if(s == null) {
			return null;
		}
		return new Sha512Hash(s, salt).toString();
	}
	
	/**
	 * sha512加密
	 * @param s 需加密的字符串
	 * @return
	 */
	public static String sha512(String s) {
		if(s == null) {
			return null;
		}
		return new Sha512Hash(s).toString();
	}

	/**
	 * 128位AES加密
	 * @param s 需加密的字符串
	 * @param key 16*n位key
	 * @return
	 */
	public static String aesEncrypt(String s, String key) {
		AesCipherService aes = new AesCipherService();
		aes.setKeySize(128);
		SecretKeySpec sks = new SecretKeySpec(key.getBytes(), "AES");
		return aes.encrypt(s.getBytes(), sks.getEncoded()).toHex();
	}
	
	/**
	 * 128位AES解密
	 * @param s 需解密的字符串
	 * @param key 16*n位key
	 * @return
	 */
	public static String aesDecrypt(String s, String key) {
		AesCipherService aes = new AesCipherService();
		aes.setKeySize(128);
		SecretKeySpec sks = new SecretKeySpec(key.getBytes(), "AES");
		log.debug("需解密的字符串：{}, 对应的key：{}", s, key);
		return new String(aes.decrypt(Hex.decode(s), sks.getEncoded()).getBytes());
	}
	
	/**
	 * 加密, 默认为md5加密
	 * @param s 需加密的字符串
	 * @param type 加密类型
	 * @param salt 加密的salt
	 * @return
	 */
	public static String encrypt(String s, String type, String salt) {
		if(s == null) {
			return null;
		}
		if(type == null) {
			return md5(s, salt);
		}
		if("base64".equalsIgnoreCase(type)) {
			return encryptBase64(s);
		}
		if("md5".equalsIgnoreCase(type)) {
			return md5(s, salt);
		}
		if("sha1".equalsIgnoreCase(type)) {
			return sha1(s, salt);
		}
		if("sha256".equalsIgnoreCase(type)) {
			return sha256(s, salt);
		}
		if("sha384".equalsIgnoreCase(type)) {
			return sha384(s, salt);
		}
		if("sha512".equalsIgnoreCase(type)) {
			return sha512(s, salt);
		}
		return md5(s, salt);
	}
	
	/**
	 * 加密, 默认为md5加密
	 * @param s 需加密的字符串
	 * @param type 加密类型
	 * @return
	 */
	public static String encrypt(String s, String type) {
		if(s == null) {
			return null;
		}
		if(type == null) {
			return md5(s);
		}
		if("base64".equalsIgnoreCase(type)) {
			return encryptBase64(s);
		}
		if("md5".equalsIgnoreCase(type)) {
			return md5(s);
		}
		if("sha1".equalsIgnoreCase(type)) {
			return sha1(s);
		}
		if("sha256".equalsIgnoreCase(type)) {
			return sha256(s);
		}
		if("sha384".equalsIgnoreCase(type)) {
			return sha384(s);
		}
		if("sha512".equalsIgnoreCase(type)) {
			return sha512(s);
		}
		return md5(s);
	}
}
