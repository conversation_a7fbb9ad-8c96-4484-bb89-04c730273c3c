package com.bonc.ids;

import cn.hutool.crypto.SecureUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @Description 应用启动入口
 * <AUTHOR>
 * @Date 2021/8/12 16:15
 */
@Slf4j
@Data
@SpringBootApplication
@EnableScheduling
@EnableAsync
public class App implements ApplicationListener<ServletWebServerInitializedEvent> {
    private static int port;


    public static void main(String[] args) {
        SecureUtil.disableBouncyCastle();
        SpringApplication.run(App.class, args);
        log.debug("应用启动完成, 对应端口：{}", App.port);
    }

    @Override
    public void onApplicationEvent(ServletWebServerInitializedEvent event) {
        App.port = event.getWebServer().getPort();
    }
}
