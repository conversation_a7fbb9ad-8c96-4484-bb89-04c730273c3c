package com.bonc.ids.aspect;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.bonc.ids.annotation.Log;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.system.BaseEvent;
import com.bonc.ids.entity.system.log.OperateLog;
import com.bonc.ids.enums.BusinessType;
import com.bonc.ids.utils.HttpUtil;
import com.bonc.ids.utils.JSONExtendUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @create 2023-12-14 9:41
 * @description 操作日志记录处理
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class LogAspect {
    private final DaoHelper daoHelper;

    /**
     * 排除敏感属性字段
     */
    public static final String[] EXCLUDE_PROPERTIES = { "password", "oldPassword", "newPassword", "confirmPassword" };


    /**
     * 计算操作消耗时间
     */
    private static final ThreadLocal<StopWatch> TIME_THREADLOCAL = new TransmittableThreadLocal<>();

    /**
     * 处理请求前执行
     */
    @Before(value = "@annotation(controllerLog)")
    public void boBefore(JoinPoint joinPoint, Log controllerLog) {
        StopWatch stopWatch = new StopWatch();
        TIME_THREADLOCAL.set(stopWatch);
        stopWatch.start();
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Log controllerLog, Object jsonResult) {
        handleLog(joinPoint, controllerLog, null, jsonResult);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Log controllerLog, Exception e) {
        handleLog(joinPoint, controllerLog, e, null);
    }

    /**
     * 处理日志
     * @param joinPoint
     * @param controllerLog
     * @param e
     * @param jsonResult
     */
    protected void handleLog(final JoinPoint joinPoint, Log controllerLog, final Exception e, Object jsonResult) {
        try {
            OperateLog operateLog = new OperateLog();
            //日志ID
            operateLog.setId(IdUtil.getSnowflakeNextId());
            // 默认请求成功
            operateLog.setStatus(1);
            operateLog.setMemo("操作成功");

            // 请求的IP
            String ip = ServletUtil.getClientIP(HttpUtil.getRequest());
            operateLog.setIp(ip);
            // 请求的接口地址
            String uri = HttpUtil.getRequest().getRequestURI();
            operateLog.setUrl(uri);
            //当前用户ID
            operateLog.setUserId(getUserId());

            if (e != null) {
                //如果存在异常，则表示请求失败
                operateLog.setStatus(0);
                operateLog.setMemo(e.getMessage());
            }
            // 请求类名
            String className = joinPoint.getTarget().getClass().getName();
            //请求方法名
            String funcName = joinPoint.getSignature().getName();
            String function = className + "." + funcName + "()";
            operateLog.setFunc(function);
            // 设置请求方式
            String method = HttpUtil.getRequest().getMethod();
            operateLog.setMethod(method);

            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, controllerLog, operateLog, jsonResult);
            // 设置消耗时间
            StopWatch stopWatch = TIME_THREADLOCAL.get();
            stopWatch.stop();
            long costTime = stopWatch.getTime();
            operateLog.setCostTime(costTime);
            // 保存数据库
            log.debug("操作日志数据：{}", operateLog);
            //发布日志事件
            SpringUtil.getApplicationContext().publishEvent(new BaseEvent(operateLog));
        } catch (Exception exp) {
            // 记录本地异常日志
            log.error("异常信息:{}", exp.getMessage());
        } finally {
            TIME_THREADLOCAL.remove();
        }
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param log     日志
     * @param operateLog 操作日志
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, Log log, OperateLog operateLog, Object jsonResult) {
        // 设置action动作
        operateLog.setBusinessType(log.businessType().ordinal());
        // 设置标题
        operateLog.setTitle(log.title());
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData()) {
            // 获取参数的信息，传入到数据库中。
            setRequestValue(joinPoint, operateLog, log.excludeParamNames());
        }
        // 是否需要保存response，参数和值
        //如果是查询操作，则默认不记录返回值
        if (log.isSaveResponseData() && ObjectUtil.isNotNull(jsonResult) && log.businessType() != BusinessType.QUERY) {
            operateLog.setResult(JSONExtendUtil.toJsonStr(jsonResult));
        }
    }

    /**
     * 获取请求的参数，放到log中
     * @param operateLog 操作日志
     */
    private void setRequestValue(JoinPoint joinPoint, OperateLog operateLog, String[] excludeParamNames) {
        Map<String, String[]> parameterMap = HttpUtil.getRequest().getParameterMap();
        Map<String, Object> params = new HashMap<>();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            params.put(entry.getKey(), StringUtils.join(entry.getValue(), ","));
        }

        String requestMethod = operateLog.getMethod();
        if (
                HttpMethod.PUT.name().equals(requestMethod) ||
                HttpMethod.POST.name().equals(requestMethod) ||
                HttpMethod.DELETE.name().equals(requestMethod) ||
                HttpMethod.PATCH.name().equals(requestMethod)
        ) {
            String paramStr = argsArrayToString(joinPoint.getArgs(), excludeParamNames);
            operateLog.setParams(paramStr);
        } else {
            MapUtil.removeAny(params, EXCLUDE_PROPERTIES);
            MapUtil.removeAny(params, excludeParamNames);
            operateLog.setParams(JSONExtendUtil.toJsonStr(params));
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray, String[] excludeParamNames) {
        StringJoiner params = new StringJoiner(" ");
        if (ArrayUtil.isEmpty(paramsArray)) {
            return params.toString();
        }
        for (Object o : paramsArray) {
            if (ObjectUtil.isNotNull(o) && !isFilterObject(o)) {
                String str = JSONExtendUtil.toJsonStr(o);
                Map<String, Object> map = JSONExtendUtil.toBean(str, Map.class);
                if (MapUtil.isNotEmpty(map)) {
                    MapUtil.removeAny(map, EXCLUDE_PROPERTIES);
                    MapUtil.removeAny(map, excludeParamNames);
                    str = JSONExtendUtil.toJsonStr(map);
                }
                params.add(str);
            }
        }
        return params.toString();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Object value : map.values()) {
                return value instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }

    /**
     * 获取当前登录账号id
     * @return
     */
    private String getUserId() {
        String userId = null;
        //先直接获取，如果出现异常，则通过token获取，token取request参数中的值
        try {
            userId = StpUtil.getLoginIdAsString();
        } catch (Exception e) {
            String token = HttpUtil.getRequest().getParameter("token");
            userId = (String) StpUtil.getLoginIdByToken(token);
        }
        return userId;
    }
}
