package com.bonc.ids.aspect;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-08-11 17:21
 * @discription 对参数进行拦截修改
 */
@Slf4j
@Configuration
@Aspect
public class GlobalParamsAspect {

    private DaoHelper daoHelper;

    public GlobalParamsAspect(DaoHelper daoHelper) {
        this.daoHelper = daoHelper;
    }

    @Pointcut("execution(* com.bonc.ids.controller..*.*(..))")
    public void executeController() {}

    @Before("executeController()")
    public void beforeMethod(JoinPoint joinPoint) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();
        Object[] objects = joinPoint.getArgs();
        for(Object o : objects) {
            if (o instanceof Map) {
                //解析页面名称和页面路径
                String pageName = request.getHeader("PAGE-NAME");
                String pagePath = request.getHeader("PAGE-PATH");
                if (!StringUtils.isEmpty(pageName)) {
                    try {
                        pageName = URLDecoder.decode(pageName, "utf-8");
                    } catch (UnsupportedEncodingException e) {
                        //不做处理
                        log.error("页面名称解析出错：{}", e.getMessage());
                    }
                }
                log.debug("当前发送请求的页面名称：{}，页面路径：{}", pageName, pagePath);
                //下面代码只有登录成功才执行
                if (!StpUtil.isLogin()) {
                    return;
                }
                Map<String, Object> user = (Map<String, Object>) StpUtil.getTokenSession().get(SaSession.USER);
                if (user != null && !user.isEmpty()) {
                    log.debug("对请求参数做注入处理");
                    String userId = (String) user.get("id");
                    List<String> roles = (List<String>) user.get("roles");
                    List<Map<String, Object>> resources = (List<Map<String, Object>>) user.get("resources");
                    List<String> permissions = DataUtil.getValueList(resources, "authCode");

                    ((Map) o).put("__ROLES", roles);
                    ((Map) o).put("__OPERATOR", userId);
                    ((Map) o).put("__ACCOUNT", user.get("account"));
                    ((Map) o).put("__ORG_ID", user.get("orgId"));
                    ((Map) o).put("__DEPT_ID", user.get("deptId"));
                    ((Map) o).put("__PHONE", user.get("phone"));
                    ((Map) o).put("__IS_ADMIN", user.get("isAdmin"));
                    ((Map) o).put("__PERMISSIONS", permissions);
                }
                ((Map) o).put("__PAGE_NAME", pageName);
                ((Map) o).put("__PAGE_PATH", pagePath);
            }
        }
    }
}
