package com.bonc.ids.satoken;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-11-01 9:48
 * @description SA-TOKEN 权限接口实现
 */
public class StpInterfaceImpl implements StpInterface {
    /**
     * 权限码集合
     * @param loginId
     * @param loginType
     * @return
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        List<String> permissions = new ArrayList<>();
        Map<String, Object> user = getUser();
        List<Map<String, Object>> resources = (List<Map<String, Object>>) user.get("resources");
        if (resources != null) {
            resources.forEach(r -> {
                String permission = (String) r.get("authCode");
                String id = (String) r.get("id");
                permission = StringUtils.isEmpty(permission) ? id : permission;
                permissions.add(permission);
            });
        }
        return permissions;
    }

    /**
     * 角色集合
     * @param loginId
     * @param loginType
     * @return
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        Map<String, Object> user = getUser();
        List<String> roles = (List<String>) user.get("roles");
        return roles;
    }

    /**
     * 获取用户信息
     * @return
     */
    private Map<String, Object> getUser() {
        Map<String, Object> user = (Map<String, Object>) StpUtil.getTokenSession().get(SaSession.USER);
        return user;
    }
}
