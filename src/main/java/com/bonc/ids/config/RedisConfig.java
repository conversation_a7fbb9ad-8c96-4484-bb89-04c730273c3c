package com.bonc.ids.config;

import io.lettuce.core.ReadFrom;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.io.Serializable;
import java.util.HashSet;

/**
 * <AUTHOR>
 * @create 2022-05-19 9:21
 * @description Redis缓存配置
 */
@Configuration
public class RedisConfig {

    /**
     * 配置序列化
     * @param redisConnectionFactory
     * @return
     */
    @Bean
    @Primary
    public RedisTemplate<Serializable, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<Serializable, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        GenericJackson2JsonRedisSerializer jsonRedisSerializer = new GenericJackson2JsonRedisSerializer();
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

        redisTemplate.setKeySerializer(stringRedisSerializer);
        redisTemplate.setHashKeySerializer(stringRedisSerializer);
        redisTemplate.setValueSerializer(jsonRedisSerializer);
        redisTemplate.setHashValueSerializer(jsonRedisSerializer);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    /**
     * 配置读写分离
     * @param redisProperties
     * @return
     */
    @Bean
    public RedisConnectionFactory lettuceConnectionFactory(RedisProperties redisProperties) {
        if(ObjectUtils.isNotEmpty(redisProperties.getSentinel())){
            // 配置哨兵节点以及主节点
            RedisSentinelConfiguration redisSentinelConfiguration = new RedisSentinelConfiguration(
                    redisProperties.getSentinel().getMaster(), new HashSet<>(redisProperties.getSentinel().getNodes())
            );
            //设置redis密码
            redisSentinelConfiguration.setPassword(redisProperties.getPassword());

            // 配置读写分离
            LettucePoolingClientConfiguration lettuceClientConfiguration = LettucePoolingClientConfiguration.builder()
                    // 读写分离,这里的ReadFrom是配置Redis的读取策略,是一个枚举,包括下面选择
                    // MASTER   仅读取主节点
                    // MASTER_PREFERRED   优先读取主节点,如果主节点不可用,则读取从节点
                    // REPLICA_PREFERRED   优先读取从节点,如果从节点不可用,则读取主节点
                    // REPLICA   仅读取从节点
                    // NEAREST   从最近节点读取
                    // ANY   从任意一个从节点读取
                    .readFrom(ReadFrom.REPLICA_PREFERRED)
                    .build();

            return new LettuceConnectionFactory(redisSentinelConfiguration, lettuceClientConfiguration);
        }else if(ObjectUtils.isNotEmpty(redisProperties.getCluster())){
            // 配置集群节点
            RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration(new HashSet<>(redisProperties.getCluster().getNodes())
            );
            //设置redis密码
            redisClusterConfiguration.setPassword(redisProperties.getPassword());
            redisClusterConfiguration.setMaxRedirects(redisProperties.getCluster().getMaxRedirects());

            // 配置读写分离
            LettucePoolingClientConfiguration lettuceClientConfiguration = LettucePoolingClientConfiguration.builder()
                    // 读写分离,这里的ReadFrom是配置Redis的读取策略,是一个枚举,包括下面选择
                    // MASTER   仅读取主节点
                    // MASTER_PREFERRED   优先读取主节点,如果主节点不可用,则读取从节点
                    // REPLICA_PREFERRED   优先读取从节点,如果从节点不可用,则读取主节点
                    // REPLICA   仅读取从节点
                    // NEAREST   从最近节点读取
                    // ANY   从任意一个从节点读取
                    .readFrom(ReadFrom.REPLICA_PREFERRED)
                    .build();

            return new LettuceConnectionFactory(redisClusterConfiguration, lettuceClientConfiguration);
        }else{
            //TODO: 单节点可能有问题，之后根据测试情况处理
            RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration(redisProperties.getHost(), redisProperties.getPort());
            redisStandaloneConfiguration.setPassword(redisProperties.getPassword());
            redisStandaloneConfiguration.setDatabase(redisProperties.getDatabase());
            return new LettuceConnectionFactory(redisStandaloneConfiguration);
        }

    }
    /*@Bean
    public RedisConnectionFactory lettuceConnectionFactory(RedisProperties redisProperties) {
        // 配置哨兵节点以及主节点
        RedisSentinelConfiguration redisSentinelConfiguration = new RedisSentinelConfiguration(
            redisProperties.getSentinel().getMaster(), new HashSet<>(redisProperties.getSentinel().getNodes())
        );
        //设置redis密码
        redisSentinelConfiguration.setPassword(redisProperties.getPassword());

        // 配置读写分离
        LettucePoolingClientConfiguration lettuceClientConfiguration = LettucePoolingClientConfiguration.builder()
                // 读写分离,这里的ReadFrom是配置Redis的读取策略,是一个枚举,包括下面选择
                // MASTER   仅读取主节点
                // MASTER_PREFERRED   优先读取主节点,如果主节点不可用,则读取从节点
                // REPLICA_PREFERRED   优先读取从节点,如果从节点不可用,则读取主节点
                // REPLICA   仅读取从节点
                // NEAREST   从最近节点读取
                // ANY   从任意一个从节点读取
                .readFrom(ReadFrom.REPLICA_PREFERRED)
                .build();

        return new LettuceConnectionFactory(redisSentinelConfiguration, lettuceClientConfiguration);
    }*/
}
