package com.bonc.ids.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.github.pagehelper.PageInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * <AUTHOR>
 * @create 2020-08-10 13:19
 * @discription MySQL数据源配置
 */
@Slf4j
@Configuration
@EnableTransactionManagement
public class MySQLConfig implements TransactionManagementConfigurer {
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.druid.master")
    public DataSource getDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean
    @Primary
    public SqlSessionFactory sqlSessionFactory() {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();

        //分页拦截器
        PageInterceptor interceptor = new PageInterceptor();
        Properties properties = new Properties();
        properties.setProperty("helperDialect", "oracle");
        interceptor.setProperties(properties);
        bean.setPlugins(new Interceptor[]{interceptor});
        bean.setDataSource(getDataSource());

        //mapper配置文件目录
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try {
            bean.setMapperLocations(resolver.getResources("classpath:mysql/**/*.xml"));
            return bean.getObject();
        } catch (Exception e) {
            log.error("mysql mapper配置目录映射出错：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public TransactionManager annotationDrivenTransactionManager() {
        return new DataSourceTransactionManager(getDataSource());
    }
}
