package com.bonc.ids.advice;

import cn.dev33.satoken.exception.SaTokenException;
import com.bonc.ids.entity.ResultEntity;
import com.bonc.ids.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @create 2020-08-10 16:16
 * @discription 全局Controller异常捕获
 */
@Slf4j
@RestControllerAdvice
public class ControllerExceptionAdvice {

    /**
     * 全局Controller异常拦截
     * @param e
     * @return
     */
    @ExceptionHandler(Exception.class)
    public ResultEntity exceptionHandler(Exception e) {
        log.error("请求出错：{}", e.getMessage());
        String active = SpringContextUtil.getActiveProfile();
        String msg = "请求出错";
        if ("dev".equals(active)) {
            msg = msg + ": " + e.getMessage().substring(0, 100);
        }
        return ResultEntity.error(msg);
    }

    /**
     * SaToken专属异常处理
     * @param e
     * @return
     */
    @ExceptionHandler(SaTokenException.class)
    public ResultEntity notLoginHandler(SaTokenException e) {
        int code = e.getCode();
        log.error("SaToken认证异常, code:{}, msg：{}", code, e.getMessage());

        if (code == 11011) {
            return new ResultEntity(401, "未认证");
        }
        if (code == 11012 || code == 11013 || code == 11016) {
            return new ResultEntity(401, "登录已失效");
        }
        if (code == 11014) {
            return new ResultEntity(401, "已在别处登录，若非本人操作请立即修改密码");
        }
        if (code == 11015) {
            return new ResultEntity(401, "已被强制下线");
        }
        if (code == 11041 || code == 11051) {
            return new ResultEntity(403, "无访问权限");
        }

        return ResultEntity.error(e.getMessage());
    }

}
