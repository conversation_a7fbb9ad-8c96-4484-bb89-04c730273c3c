package com.bonc.ids.listener;

import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.system.BaseEvent;
import com.bonc.ids.entity.system.log.LoginLog;
import com.bonc.ids.entity.system.log.OperateLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024-01-16 10:59
 * @description 事件监听器
 */
@Slf4j
@Component
@EnableAsync
@RequiredArgsConstructor
public class BaseEventListener {
    private final DaoHelper daoHelper;

    /**
     * 操作日志记录
     * @param event
     */
    @Async
    @EventListener
    public void handleOperateEvent(BaseEvent<OperateLog> event) {
        log.debug("接收到操作日志事件了：{}", event);
        saveData("mapper.log.saveOperateLog", event.getEntity());
    }

    /**
     * 登录日志记录
     * @param event
     */
    @Async
    @EventListener
    public void handleLoginEvent(BaseEvent<LoginLog> event) {
        log.debug("接收到登录日志事件了：{}", event);
        saveData("mapper.log.saveLoginLog", event.getEntity());
    }

    /**
     * 保存数据
     * @param statement
     * @param entity
     */
    private <T> void saveData(String statement, T entity) {
        try {
            this.daoHelper.insert(statement, entity);
        } catch (Exception e) {
            log.error("日志记录出错：{}", e.getMessage());
        }
    }
}
