package com.bonc.ids.controller;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.bonc.ids.constant.Constants;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.ResultCode;
import com.bonc.ids.entity.ResultEntity;
import com.bonc.ids.entity.system.BaseEvent;
import com.bonc.ids.entity.system.log.LoginLog;
import com.bonc.ids.utils.*;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-08-10 12:49
 * @discription
 */
@Slf4j
@RestController
@RequestMapping("/login")
public class LoginController {
    private Environment environment;
    private DaoHelper daoHelper;
    @Value("${app.cipher-key:GoFuckYourself!!}")
    private String cipherKey;

    public LoginController(Environment environment, DaoHelper daoHelper) {
        this.environment = environment;
        this.daoHelper = daoHelper;
    }

    /**
     * 登录认证（本系统登录）
     * @return
     */
    @PostMapping
    public ResultEntity signIn(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        log.debug("登录认证参数：{}", params);
        if (params.isEmpty()) {
            return ResultEntity.error("非法操作");
        }

        //因为前台的所有参数都是加密的，这里先进行AES解密
        String info = (String) params.get("info");
        if (StringUtils.isEmpty(info)) {
            return ResultEntity.error("非法操作");
        }

        String decryptInfo = CryptoUtil.decrypt(info, cipherKey);
        if (StringUtils.isEmpty(decryptInfo)) {
            return ResultEntity.error("非法操作");
        }
        log.debug("解密后的认证参数：{}", decryptInfo);
        Map<String, Object> newParams = JSONExtendUtil.toBean(decryptInfo, Map.class);

        String account = (String) newParams.get("account");
        String password = (String) newParams.get("password");
        String code = (String) newParams.get("captcha");
        String signType = (String) newParams.get("signType");
        //账号是否为空
        if (StringUtils.isEmpty(account)) {
            return ResultEntity.error("请填写登录账号");
        }
        //密码是否为空
        if (StringUtils.isEmpty(password)) {
            return ResultEntity.error("请填写认证密码");
        }
        //验证码是否为空
        if (StringUtils.isEmpty(code)) {
            return ResultEntity.error("请填写验证码");
        }
        //校验登录类型
        if (!"SYS".equalsIgnoreCase(signType)) {
            return ResultEntity.error("非法操作");
        }
        //验证码是否正确
        ResultEntity checkResult = compareCaptcha(newParams);
        if (checkResult.getCode() != ResultCode.SUCCESS.code) {
            return checkResult;
        }
        return commonLogin(newParams);
    }

    /**
     * 单点登录认证
     * @param params
     * @return
     */
    @PostMapping("sso")
    public ResultEntity sso(@RequestBody Map<String, Object> params) throws Exception {
        log.debug("单点登录认证参数: {}", params);
        String signType = (String) params.get("signType");
        if ("UNICOM_DSS_SSO".equalsIgnoreCase(signType)) {
            //集团经分4.0
            return unicomV4SSO(params);
        }
        if ("WISDOM_SSO".equalsIgnoreCase(signType)) {
            //智慧运营大屏
            return widomSSO(params);
        }
        if ("ZQ_SSO".equalsIgnoreCase(signType)) {
            //政企运营平台
            return zqSSO(params);
        }
        if ("AI_PORTAL".equalsIgnoreCase(signType)) {
            //联通智慧门户
            return aiPortal(params);
        }
        return ResultEntity.error("非法操作");
    }

    /**
     * 联通集团经分4.0登录认证
     * @param params
     * @return
     */
    private ResultEntity unicomV4SSO(Map<String, Object> params) throws Exception {
        log.debug("联通集团经分4.0登录认证参数: {}", params);
        String token = (String) params.get("ssoToken");
        //如果请求不包含token则前端进入系统登录页面
        if (StringUtils.isEmpty(token)) {
            return ResultEntity.error("认证失败");
        }
        //根据token调用接口获取用户信息
        SSLUtils.ignoreSsl();
        Map<String, Object> result = HttpUtil.getUserInfo(token);
        int code = ConvertUtil.toInt(result.get("code"));
        String msg = (String) result.get("msg");
        if (code != 200) {
            return ResultEntity.error(msg);
        }
        Map<String, Object> user = (Map<String, Object>) result.get("user");
        if (user == null || user.isEmpty()) {
            return ResultEntity.error("账号不存在");
        }
        String status = (String) user.get("status");
        String delFlag = (String) user.get("delFlag");
        if (!"0".equals(status) && !"0".equals(delFlag)) {
            return ResultEntity.error("账号已停用或已注销");
        }
        //当前登录的账号
        String account = (String) user.get("userName");
        //如果登录账号为hn2-admin、hi-jingfgly则转换为admin
        if ("hn2-admin".equalsIgnoreCase(account) || "hi-jingfgly".equalsIgnoreCase(account)) {
            account = "admin";
        }
        params.put("account", account);
        return commonLogin(params);
    }

    /**
     * 智慧大屏
     * @param params
     * @return
     */
    private ResultEntity widomSSO(Map<String, Object> params) {
        log.debug("智慧大屏登录认证参数: {}", params);
        String token = (String) params.get("ssoToken");
        //如果请求不包含token则前端进入系统登录页面
        if (StringUtils.isEmpty(token)) {
            return ResultEntity.error("认证失败");
        }
        String account = JWTUtil.parseToken(token).getPayload("sub").toString();
        if (StringUtils.isEmpty(account)) {
            return ResultEntity.error("账号不存在");
        }
        params.put("account", account);
        return commonLogin(params);
    }

    /**
     * 政企运营平台
     * @param params
     * @return
     */
    private ResultEntity zqSSO(Map<String, Object> params) {
        log.debug("政企运营平台单点认证参数：{}", params);
        String url = environment.getProperty("app.unicom-zq-api.auth");
        String sessionId = (String) params.get("sessionId");
        if (StringUtils.isEmpty(sessionId)) {
            return ResultEntity.error("缺少sessionId");
        }
        Object o = HttpUtil.get(url + sessionId);
        log.debug("政企运营平台 - 认证接口返回数据：{}", o);
        //resultCode=0表示成功，其他都是失败
        String code = JSONExtendUtil.getValueByKey(o, "$.resultCode");
        if (!"0".equals(code)) {
            return ResultEntity.error("认证失败");
        }
        //目前只需要账号信息
        String account = JSONExtendUtil.getValueByKey(o, "$.resultObject.userInfo.login");
        if (StringUtils.isEmpty(account)) {
            return ResultEntity.error("认证失败");
        }
        //如果账号为hi-jingfgly则转换为admin
        if ("hi-jingfgly".equalsIgnoreCase(account)) {
            account = "admin";
        }
        params.put("account", account);
        return commonLogin(params);
    }

    /**
     * 联通智慧门户
     * @param params
     * @return
     */
    private ResultEntity aiPortal(Map<String, Object> params) {
        log.debug("联通智慧门户单点认证参数：{}", params);
        String token = (String) params.get("ssoToken");
        if (StringUtils.isEmpty(token)) {
            return ResultEntity.error("未认证的请求");
        }
        //智慧门户是通过自己的应用生成jwt令牌后跳转，因此这里直接校验令牌
        String key = environment.getProperty("app.ai-portal-sso-jwt-key");
        //校验token是否有效
        JWT jwt = JWT.of(token).setKey(key.getBytes());
        boolean isActive = jwt.validate(0);
        if (!isActive) {
            return ResultEntity.error("无效的令牌");
        }
        //获取账号信息
        String account = (String) jwt.getPayload("sub");
        if (StringUtils.isEmpty(account)) {
            return ResultEntity.error("无效的账号");
        }
        params.put("account", account);
        return commonLogin(params);
    }

    /**
     * 生成验证码
     */
    @GetMapping("captcha")
    public Map<String, Object> captcha() {
        // 三个参数分别为宽、高、位数
        SpecCaptcha specCaptcha = new SpecCaptcha(100, 38, 4);
        // 设置类型，字母数字混合
        specCaptcha.setCharType(Captcha.TYPE_DEFAULT);
        // 验证码内容
        String code = specCaptcha.text();
        String uuid = IdUtil.fastSimpleUUID();

        Map<String, Object> result = new HashMap<>();
        result.put("random", uuid);
        result.put("base64", specCaptcha.toBase64());
        log.debug("当前生成的验证码：{}", code);
        //将验证码存入redis，有效期60s
        String key = Constants.CAPTCHA_KEY_PREFIX + uuid;
        RedisUtil.set(key, code, Constants.CAPTCHA_EXPIRATION);
        return result;
    }

    /**
     * 校验验证码
     * @param params
     * @return
     */
    @PostMapping("captcha")
    public ResultEntity checkCaptcha(@RequestBody Map<String, Object> params) {
        log.debug("校验验证码参数：{}", params);
        //不失效验证码
        params.put("doNotCancelCaptcha", 1);
        return compareCaptcha(params);
    }

    /**
     * 判断验证码是否过期
     * @param params
     * @return
     */
    private ResultEntity compareCaptcha(Map<String, Object> params) {
        String code = (String) params.get("captcha");
        String random = (String) params.get("random");
        if (StringUtils.isEmpty(code) || StringUtils.isEmpty(random)) {
            return ResultEntity.error("参数错误");
        }
        String key = Constants.CAPTCHA_KEY_PREFIX + random;
        //提取redis中存储的验证码
        String captcha = RedisUtil.get(key);
        if (StringUtils.isEmpty(captcha)) {
            return ResultEntity.error("验证码错误或已过期");
        }
        //验证码并比对
        log.debug("当前存储的验证码：{}", captcha);
        if (!code.equalsIgnoreCase(captcha)) {
            return ResultEntity.error("验证码错误或已过期");
        }
        //失效验证码
        int doNotCancelCaptcha = ConvertUtil.toInt(params.get("doNotCancelCaptcha"));
        if (doNotCancelCaptcha == 0) {
            RedisUtil.delete(key);
        }
        return ResultEntity.success("校验通过");
    }

    /**
     * 登录统一认证处理
     * @param params
     * @return
     */
    private ResultEntity commonLogin(Map<String, Object> params) {
        String account = (String) params.get("account");
        String password = (String) params.get("password");
        String signType = (String) params.get("signType");

        LoginLog loginLog = new LoginLog();
        HttpServletRequest request = HttpUtil.getRequest();
        UserAgent userAgent = UserAgentUtil.parse(request.getHeader("User-Agent"));
        //登录日志
        loginLog.setId(IdUtil.getSnowflakeNextIdStr());
        loginLog.setAccount(account);
        //客户端IP
        String ip = ServletUtil.getClientIP(request);
        loginLog.setIp(ip);
        //客户端浏览器
        loginLog.setBrowser(userAgent.getBrowser().getName());
        //客户端操作系统
        loginLog.setOs(userAgent.getOs().getName());
        //登录类型
        loginLog.setSignType(signType);
        //登录结果标识
        loginLog.setSuccess(0);

        boolean needCheckPassword = "SYS".equalsIgnoreCase(signType); //系统直登需要检查密码
        //获取用户信息
        Map<String, Object> user = this.daoHelper.queryForObject("mapper.security.getUserByAccount", params);
        if (user == null || user.isEmpty()) {
            String memo = needCheckPassword ? "账号或密码错误" : "账号不存在";
            loginLog.setMemo(memo);
            SpringUtil.getApplicationContext().publishEvent(new BaseEvent<>(loginLog));
            log.error("账号不存在，登录参数：{}", params);
            return ResultEntity.error(memo);
        }
        //账号是否有效
        int state = ConvertUtil.toInt(user.get("state"));
        if (state == 0) {
            //更新登录日志
            String memo = needCheckPassword ? "账号或密码错误" : "账号已停用";
            loginLog.setMemo(memo);
            SpringUtil.getApplicationContext().publishEvent(new BaseEvent<>(loginLog));
            log.error("账号已停用，登录参数：{}", params);
            return ResultEntity.error(memo);
        }
        if (needCheckPassword) {
            //比对密码（前台的密码做了SHA1处理）
            String sysPassword = (String) user.get("password");
            String salt = (String) user.get("salt");
            log.info("salt：{}", RandomUtil.randomString(16));
            log.info("请求密码：{}，加密后为：{}",password,EncryptUtil.aesEncrypt(password, salt));
            String decryptPassword = EncryptUtil.aesDecrypt(sysPassword, salt);
            if (StringUtils.isEmpty(decryptPassword) || !decryptPassword.equals(password)) {
                String memo = "账号或密码错误";
                loginLog.setMemo(memo);
                SpringUtil.getApplicationContext().publishEvent(new BaseEvent<>(loginLog));
                log.error("密码错误，登录参数：{}", params);
                return ResultEntity.error(memo);
            }
        }
        log.debug("认证成功，开始生成token");
        // 更新用户对应的新密码字段，用于过度到新的加密策略 2024-01-31
        /*String password2 = (String) params.get("password2");
        Map<String, Object> newParams = new HashMap<>();
        newParams.put("password", BCrypt.hashpw(password2));
        newParams.put("account", account);
        this.updateNewPassword(newParams);*/

        //认证成功
        user.remove("password");
        user.remove("salt");
        String userId = (String) user.get("id");

        StpUtil.login(userId, "PC");
        StpUtil.getTokenSession().set(SaSession.USER, user);
        String token = StpUtil.getTokenInfo().getTokenValue();
        //token失效时间（返回给前端用于页面跳转判断）
        long tokenTimeout = StpUtil.getTokenTimeout();
        Date tokenExpireTime = DateExtendUtil.offsetSecond(new Date(), ConvertUtil.toInt(tokenTimeout));

        loginLog.setSuccess(1);
        loginLog.setMemo("登录成功");
        loginLog.setToken(token);
        loginLog.setLogoutTime(tokenExpireTime);
        SpringUtil.getApplicationContext().publishEvent(new BaseEvent<>(loginLog));

        //将用户信息返回前端
        Map<String, Object> result = new HashMap<>();
        result.put("user", user);
        result.put("token", token);
        result.put("expireTime", DateExtendUtil.format(tokenExpireTime, "yyyy-MM-dd HH:mm:ss"));
        result.put("signTime", DateExtendUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));

        return ResultEntity.success("认证成功", result);
    }

    /**
     * 更新用户对应的新密码字段，用于过度到新的加密策略 2024-01-31
     * @param params
     */
    private void updateNewPassword(Map<String, Object> params) {
        Thread t = new Thread(() -> {
            try {
                this.daoHelper.update("mapper.security.updateNewPassword", params);
            } catch (Exception e) {
                log.debug("采用新的加密策略更新密码出错，更新参数：{}，错误信息：{}", params, e.getMessage());
            }
        });
        t.start();
    }
}
