package com.bonc.ids.controller;

import cn.hutool.core.util.RandomUtil;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 忘记密码
 */
@Slf4j
@RestController
@RequestMapping("/forget")
public class ForgetController {
    private DaoHelper daoHelper;
    @Value("${app.cipher-key:GoFuckYourself!!}")
    private String cipherKey;
    @Value("${app.password-expire:90}")
    private long passwordExpire;

    public ForgetController(DaoHelper daoHelper) {
        this.daoHelper = daoHelper;
    }

    /**
     * 发送短信验证码
     * @param params
     * @return
     */
    @PostMapping
    public ResultEntity send(@RequestBody Map<String, Object> params) {
        log.debug("短信验证码发送参数：{}", params);
        String account = (String) params.get("account");
        String phone = (String) params.get("phone");
        if (StringUtils.isEmpty(account)) {
            return ResultEntity.error("请填写账号");
        }
        if (StringUtils.isEmpty(phone)) {
            return ResultEntity.error("请填写绑定的手机号");
        }
        Map<String, Object> user = (Map<String, Object>) this.daoHelper.queryForObject("mapper.security.getUserByAccount", params);
        //账号是否存在
        if (user == null || user.isEmpty()) {
            return ResultEntity.error("账号不存在");
        }
        String bindPhone = (String) user.get("phone");
        //是否绑定手机号
        if (StringUtils.isEmpty(bindPhone)) {
            return ResultEntity.error("账号未绑定任何手机号，请联系管理员");
        }
        //账号与手机号是否匹配
        if (!bindPhone.equals(phone)) {
            return ResultEntity.error("与绑定的手机号不匹配");
        }
        //检查是否存在有效的验证码
        String captcha = this.daoHelper.queryForObject("mapper.security.getSmsCaptcha", params);
        if (StringUtils.isEmpty(captcha)) {
            captcha = RandomUtil.randomNumbers(6);
            params.put("flag", 0);
        } else {
            params.put("flag", 1);
        }
        params.put("captcha", captcha);
        this.daoHelper.insert("mapper.security.sendSmsCaptcha", params);
        return ResultEntity.success("发送成功");
    }

    /**
     * 检查短信验证码
     * @param params
     * @return
     */
    @PutMapping
    public ResultEntity check(@RequestBody Map<String, Object> params) {
//        log.debug("检查短信验证码参数：{}", params);
//        int flag = (int) this.daoHelper.queryForObject("mapper.security.checkSmsCaptcha", params);
//        if (flag != 0) {
//            try {
//                this.daoHelper.update("mapper.security.invalidCaptcha", params);
//            } catch (Exception e) {
//                //不做处理
//                log.error("手动失效验证码出错：{}", e.getMessage());
//            }
//            //生成一个以当前时间戳为key的加密字符串返回前端，用于重置阶段校验
//            String account = (String) params.get("account");
//            long timestamp = System.currentTimeMillis();
//            String random = EncryptUtil.aesEncrypt(account, cipherKey);
//            Map<String, Object> result = new HashMap<>();
//            result.put("timestamp", timestamp);
//            result.put("random", random);
//            return ResultEntity.success("验证通过", result);
//        }
        return ResultEntity.error("验证码错误");
    }

    /**
     * 重置密码
     * @param params
     * @return
     */
    @PatchMapping
    public ResultEntity reset(@RequestBody Map<String, Object> params) {
//        log.debug("密码重置参数：{}", params);
//        String password = (String) params.get("password");
//        String password2 = (String) params.get("confirmPassword");
//        String random = (String) params.get("random");
//        long timestamp = Convert.toLong(params.get("timestamp"));
//        //检查新密码是否为空
//        if (StringUtils.isEmpty(password)) {
//            return ResultEntity.error("请输入新的登录密码");
//        }
//        //检查确认密码是否与新密码相同
//        if (!password.equals(password2)) {
//            return ResultEntity.error("两次密码输入不一致");
//        }
//        //检查本次重置操作是否合法（是否通过短信校验）
//        long current = System.currentTimeMillis();
//        log.debug("加密时间戳：{}， 当前时间戳：{}", timestamp, current);
//        long minus = (current - timestamp) / 1000 / 60;
//        if (minus < 0 || minus > 10) {
//            //10分钟内有效
//            return ResultEntity.error("操作超时，请重新进行上一步操作");
//        }
//        //短信校验比对
//        String account1 = EncryptUtil.aesDecrypt(random, cipherKey);
//        String account = (String) params.get("account");
//        if (StringUtils.isEmpty(account) || !account.equals(account1)) {
//            return ResultEntity.error("非法操作");
//        }
//        //检查当前用户是否存在
//        Map<String, Object> user = (Map<String, Object>) this.daoHelper.queryForObject("mapper.security.getUserByAccount", params);
//        if (user == null || user.isEmpty()) {
//            return ResultEntity.error("当前账号不存在");
//        }
//        String salt = RandomUtil.randomString(16);
//        String encryptPassword = EncryptUtil.aesEncrypt(password, salt);
//        params.put("password", encryptPassword);
//        params.put("salt", salt);
//        params.put("passwordExpire", passwordExpire);
//        this.daoHelper.update("mapper.security.resetPassword", params);
        return ResultEntity.success("重置成功");
    }
}
