package com.bonc.ids.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.error.SaErrorCode;
import cn.dev33.satoken.exception.SaTokenException;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.RandomUtil;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.ResultCode;
import com.bonc.ids.entity.ResultEntity;
import com.bonc.ids.utils.AjaxUtil;
import com.bonc.ids.utils.COSUtil;
import com.bonc.ids.utils.EncryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2020-08-11 下午6:38
 */
@RequestMapping("/common")
@RestController
@Slf4j
public class CommonController {
    private DaoHelper daoHelper;

    @Value("${app.password-expire:90}")
    private long passwordExpire;

    public CommonController(DaoHelper daoHelper) {
        this.daoHelper = daoHelper;
    }

    /**
     * 修改密码
     * @param params
     * @return
     */
    @PostMapping("password")
    public ResultEntity modifyPassword(@RequestBody Map<String, Object> params) {
        log.debug("修改密码参数：{}", params);
        String password = (String) params.get("password");
        String newPassword = (String) params.get("newPassword");
        String confirmPassword = (String) params.get("confirmPassword");
        //检查当前密码是否为空
        if (StringUtils.isEmpty(password)) {
            return ResultEntity.error("请输入当前登录密码");
        }
        //检查新密码是否为空
        if (StringUtils.isEmpty(newPassword)) {
            return ResultEntity.error("请输入新的登录密码");
        }
        //检查确认密码是否与新密码相同
        if (!newPassword.equals(confirmPassword)) {
            return ResultEntity.error("两次密码输入不一致");
        }
        //检查当前用户是否存在
        Map<String, Object> user = (Map<String, Object>) this.daoHelper.queryForObject("mapper.security.getUserByAccount", params);
        if (user == null || user.isEmpty()) {
            return ResultEntity.error("当前账号不存在");
        }
        //检查当前密码是否正确
        String sysPassword = (String) user.get("password");
        String sysSalt = (String) user.get("salt");
        String decryptPassword = EncryptUtil.aesDecrypt(sysPassword, sysSalt);
        if (!password.equals(decryptPassword)) {
            return ResultEntity.error("当前登录密码错误");
        }
        //检查新旧密码是否一样
        if (newPassword.equals(password)) {
            return ResultEntity.error("不能跟旧密码一样");
        }
        //保存新的密码
        String newSalt = RandomUtil.randomString(16);

        String encryptPassword = EncryptUtil.aesEncrypt(newPassword, newSalt);
        params.put("password", encryptPassword);
        params.put("salt", newSalt);
        params.put("expire", passwordExpire);
        this.daoHelper.update("mapper.security.modifyPassword", params);
        return ResultEntity.success("修改成功");
    }

    /**
     * 附件下载
     * 因为SA-TOKEN不支撑参数形式的令牌，这里做免认证，在方法体中进行判断
     * @param params
     */
    @SaIgnore
    @GetMapping("attachment")
    public void attachment(@RequestParam Map<String, Object> params, HttpServletResponse response) {
        log.debug("附件下载参数：{}", params);
        String fileName = (String) params.get("fileName");
        String originalName = (String) params.get("originalName");
        String bucket = (String) params.get("bucket");
        String token = (String) params.get("token");

        if (StringUtils.isEmpty(fileName) || StringUtils.isEmpty(token)) {
            AjaxUtil.response("参数错误", response);
            return;
        }
        //校验token合法性
        Object loginId = StpUtil.getLoginIdByToken(token);
        if (loginId == null) {
            throw new SaTokenException(SaErrorCode.CODE_11011);
        }
        String newName = StringUtils.isEmpty(originalName) ? fileName : originalName;
        ResultEntity entity = StringUtils.isEmpty(bucket) ? COSUtil.getURL(fileName) : COSUtil.getURL(bucket, fileName);
        Map<String, Object> data = (Map<String, Object>) entity.getData();
        String urlStr = (String) data.get("url");
        String contentType = (String) data.get("contentType");
        if (entity.getCode() != ResultCode.SUCCESS.code || StringUtils.isEmpty(urlStr)) {
            AjaxUtil.response("文件下载失败", response);
            return;
        }
        try {
            URL url = new URL(urlStr);
            URLConnection conn = url.openConnection();
            InputStream inputStream = conn.getInputStream();

            response.reset();
            response.setContentType(contentType);
            response.addHeader("Content-Disposition", "inline; filename=" + URLEncoder.encode(newName, "UTF-8"));
            ServletOutputStream outputStream = response.getOutputStream();
            byte[] b = new byte[1024];
            int len;
            //从输入流中读取一定数量的字节，并将其存储在缓冲区字节数组中，读到末尾返回-1
            while ((len = inputStream.read(b)) > 0) {
                outputStream.write(b, 0, len);
            }
            inputStream.close();
        } catch (Exception e) {
            log.error("文件下载失败：{}", e.getMessage());
            AjaxUtil.response("文件下载失败", response);
        }
    }

    /**
     * 根据token获取用户信息
     * @param token
     * @return
     */
    @SaIgnore
    @GetMapping("user-by-token")
    public ResultEntity getUserByToken(String token) {
        if (StringUtils.isEmpty(token)) {
            throw new SaTokenException(SaErrorCode.CODE_11011);
        }
        //校验token合法性
        Object loginId = StpUtil.getLoginIdByToken(token);
        if (loginId == null) {
            throw new SaTokenException(SaErrorCode.CODE_11011);
        }
        Object user = StpUtil.getTokenSessionByToken(token).get(SaSession.USER);
        return ResultEntity.success(user);
    }
}

