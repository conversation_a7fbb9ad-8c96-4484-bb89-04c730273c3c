package com.bonc.ids.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.bonc.ids.annotation.Log;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.enums.BusinessType;
import com.bonc.ids.utils.DateExtendUtil;
import com.bonc.ids.utils.JSONExtendUtil;
import com.bonc.ids.utils.PageUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 意见反馈管理
 *
 * <AUTHOR>
 * @date 2025-07-25 10:40:51
 */

@Slf4j
@RequestMapping("/system/feed")
@RestController
public class FeedController {

    private DaoHelper daoHelper;

    public FeedController(Dao<PERSON>elper daoHelper) {
        this.daoHelper = daoHelper;
    }

    /**
     * 意见反馈列表
     *
     * @param params
     * @return
     */
    @GetMapping
    @SaCheckPermission(value = {"sys:feed:view", "bonc:demand:view"}, mode = SaMode.OR)
    public PageInfo<?> query(@RequestParam Map<String, Object> params) {
        log.debug("意见反馈查询参数：{}", params);
        DateExtendUtil.setOrgDate(this.daoHelper, params);
        PageUtil.startPage(params);
        PageInfo<?> pageInfo = this.daoHelper.queryForPageList("mapper.feed.getList", params);
        return pageInfo;
    }

    /**
     * 处理反馈意见
     *
     * @param params
     * @return
     */
    @Log(title = "意见反馈管理", businessType = BusinessType.UPDATE)
    @PatchMapping
    @SaCheckPermission("sys:feed:handle")
    public void handle(@RequestBody Map<String, Object> params) {
        log.debug("处理反馈意见参数：{}", params);

        // 获取当前登录用户信息
        if (StpUtil.isLogin()) {
            log.info("当前用户已登录，开始获取用户信息...");
            Map<String, Object> currentUser = (Map<String, Object>) StpUtil.getTokenSession().get(SaSession.USER);
            log.info("当前用户信息：{}", JSONExtendUtil.toJsonStr(currentUser));
            if (currentUser != null && !currentUser.isEmpty()) {
                String userId = (String) currentUser.get("id");
                String userName = (String) currentUser.get("name");
                String account = (String) currentUser.get("account");
                log.debug("当前操作用户：ID={}, 姓名={}, 账号={}", userId, userName, account);
                // 将操作人信息添加到参数中
                params.put("handlerId", userId);
                params.put("handlerName", userName);
            }
        }


        this.daoHelper.update("mapper.feed.handle", params);
    }

    /**
     * 根据ID查询反馈意见详情
     */
    @GetMapping("detail")
    @SaCheckPermission("sys:feed:detail")
    public Map<String, Object> detail(@RequestParam Map<String, Object> params) {
        log.debug("意见反馈详情查询参数：{}", params);
        DateExtendUtil.setOrgDate(this.daoHelper, params);
        Map<String, Object> detail = this.daoHelper.queryForObject("mapper.feed.getDetail", params);
        return detail;
    }

    /**
     * 删除意见反馈（逻辑删除）
     *
     * @param params
     * @return
     */
    @Log(title = "意见反馈管理", businessType = BusinessType.DELETE)
    @DeleteMapping
    @SaCheckPermission("sys:feed:del")
    public void remove(@RequestBody Map<String, Object> params) {
        log.debug("意见反馈删除参数：{}", params);
        this.daoHelper.update("mapper.feed.delete", params);
    }

}

