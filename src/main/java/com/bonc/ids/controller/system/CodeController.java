package com.bonc.ids.controller.system;

import com.bonc.ids.dao.DaoHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-08-11 9:51
 * @discription 码表接口
 */
@Slf4j
@RestController
@RequestMapping("/system/code")
public class CodeController {
    private DaoHelper daoHelper;

    public CodeController(DaoHelper daoHelper) {
        this.daoHelper = daoHelper;
    }

    /**
     * 获得所有部门列表
     *
     * @param params
     * @return
     */
    @GetMapping("department")
    public List<?> getDepartments(@RequestParam(required = false) Map<String, Object> params) {
        List<?> list = this.daoHelper.queryForList("mapper.code.getDepartments", params);
        return list;
    }

    /**
     * 获得所有区县列表
     *
     * @param params
     * @return
     */
    @GetMapping("region")
    public List<?> getRegions(@RequestParam(required = false) Map<String, Object> params) {
        List<?> list = this.daoHelper.queryForList("mapper.code.getRegions", params);
        return list;
    }

    /**
     * 获得所有职务码表
     *
     * @param params
     * @return
     */
    @GetMapping("position")
    public List<?> getPositions(@RequestParam(required = false) Map<String, Object> params) {
        List<?> list = this.daoHelper.queryForList("mapper.code.getPositions", params);
        return list;
    }
    
    
    /**
     * 获得地市码表
     *
     * @param params
     * @return
     */
    @GetMapping("citys")
    public List<?> getCitys(@RequestParam(required = false) Map<String, Object> params) {
        List<?> list = this.daoHelper.queryForList("mapper.code.getCitys", params);
        return list;
    }
    
}
