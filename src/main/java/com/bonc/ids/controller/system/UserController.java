package com.bonc.ids.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.bonc.ids.annotation.Log;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.ResultEntity;
import com.bonc.ids.enums.BusinessType;
import com.bonc.ids.utils.*;
import com.github.liaochong.myexcel.core.DefaultExcelBuilder;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 用户管理模块，对用户进行管理操作
 * @createTime 2020-08-11 上午9:27
 */

@Slf4j
@RequestMapping("/system/user")
@RestController
public class UserController {
    private DaoHelper daoHelper;
    private Environment environment;

    public UserController(DaoHelper daoHelper, Environment environment) {
        this.daoHelper = daoHelper;
        this.environment = environment;
    }

    /**
     * 用户列表
     *
     * @param params
     * @return
     */
    @GetMapping
    @SaCheckPermission(value = {"sys:user:view", "bonc:demand:view"}, mode = SaMode.OR)
    public PageInfo<?> query(@RequestParam Map<String, Object> params) {
        log.debug("用户信息查询参数：{}", params);
        DateExtendUtil.setOrgDate(this.daoHelper, params);
        //非管理员只能看到自己和自己创建的用户
        PageUtil.startPage(params);
        PageInfo<?> pageInfo = this.daoHelper.queryForPageList("mapper.user.getList", params);
//        PageInfo<?> pageInfo = this.daoHelper.queryForPageList("mapper.user.getUserByUserAccount", params);
        return pageInfo;
    }

    /**
     * 保存用户
     *
     * @param params
     * @return
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @SaCheckPermission(value = {"sys:user:add", "sys:user:edit"}, mode = SaMode.OR)
    public void save(@RequestBody Map<String, Object> params) {
        log.debug("用户保存参数：{}", params);
        String id = (String) params.get("id");
        String statement = "modify";
        if (StringUtils.isEmpty(id)) {
//            id = UUID.randomUUID().toString();
            id = String.valueOf(IdUtil.getSnowflakeNextId());

            params.put("id", id);
            statement = "add";
        }
        String password = (String) params.get("password");
        //如果密码不为空,加密密码
        if (!StringUtils.isEmpty(password)) {
            //密码有效时间（天）
            int expire = ConvertUtil.toInt(environment.getProperty("app.password-expire", "90"));
            //获得加密盐
            String salt = RandomUtil.randomString(16);
            params.put("salt", salt);
            params.put("password", EncryptUtil.aesEncrypt(password, salt));
            params.put("expire", expire);
        }
        this.daoHelper.update("mapper.user." + statement, params);
    }

    /**
     * 删除用户
     *
     * @param params
     * @return
     */
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping
    @SaCheckPermission("sys:user:del")
    public void remove(@RequestBody Map<String, Object> params) {
        log.debug("用户信息删除参数：{}", params);
        this.daoHelper.delete("mapper.user.delete", params);
    }

    /**
     * 禁用、启用
     *
     * @param params
     * @return
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PatchMapping
    @SaCheckPermission(value = {"sys:user:enable", "sys:user:disable"}, mode = SaMode.OR)
    public void patch(@RequestBody Map<String, Object> params) {
        log.debug("启用、禁用参数：{}", params);
        this.daoHelper.update("mapper.user.updateState", params);
    }

    /**
     * 重置密码
     *
     * @param params
     * @return
     */
    @Log(title = "用户管理", businessType = BusinessType.RESET)
    @PostMapping("password")
    @SaCheckPermission("sys:user:reset")
    public void resetPassword(@RequestBody Map<String, Object> params) {
        log.debug("重置密码参数：{}", params);
        String password = (String) params.get("password");
        String tip = "";
        if (StringUtils.isEmpty(password)) {
            //如果没有设定密码则系统随机生成
            password = RandomUtil.randomString(8);
            tip = "，密码为：" + password;
            password = EncryptUtil.sha1(password);
        }
        //密码有效时间（天）
        int expire = ConvertUtil.toInt(environment.getProperty("app.password-expire", "90"));
        //生成加密盐
        String salt = RandomUtil.randomString(16);
        params.put("salt", salt);
        params.put("password", EncryptUtil.aesEncrypt(password, salt));
        params.put("expire", expire);
        this.daoHelper.update("mapper.user.resetPassword", params);
    }

    /**
     * 校验工号、手机号、邮箱是否重复
     *
     * @param params
     * @return
     */
    @GetMapping("field")
    public ResultEntity validField(@RequestParam Map<String, Object> params) {
        log.debug("字段校验参数：{}", params);
        int flag = this.daoHelper.queryForObject("mapper.user.validField", params);
        if (flag == 0) return ResultEntity.success("校验通过");
        return ResultEntity.error("值已存在");
    }

    /**
     * 保存角色授权
     *
     * @param params
     * @return
     */
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("role")
    @SaCheckPermission("sys:user:grant:role")
    public void saveRoles(@RequestBody Map<String, Object> params) {
        log.debug("角色授权信息：{}", params);
        this.daoHelper.update("mapper.user.saveRoles", params);
    }

    /**
     * 根据ID获取用户已授权资源
     *
     * @param params
     * @return
     */
    @GetMapping("resource")
    public List<?> getResources(@RequestParam Map<String, Object> params) {
        log.debug("已授权资源查询参数：{}", params);
        List<?> list = this.daoHelper.queryForList("mapper.user.getResources", params);
        return list;
    }

    /**
     * 保存资源授权
     *
     * @param params
     * @return
     * @throws Exception
     */
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("resource")
    @SaCheckPermission("sys:user:grant:res")
    public void saveResources(@RequestBody Map<String, Object> params) {
        log.debug("资源授权信息参数：{}", params);
        daoHelper.update("mapper.user.saveResources", params);
    }

    /**
     * 导出用户信息
     *
     * @param params
     */
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PostMapping("export")
    @SaCheckPermission("sys:user:export")
    public void export(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        log.debug("用户信息导出参数：{}", params);
        // 设置组织表默认账期
        DateExtendUtil.setOrgDate(this.daoHelper, params);
        List<Map> list = this.daoHelper.queryForList("mapper.user.getExportUserList", params);
        Map<String, String> head = new LinkedHashMap<>();
        head.put("account", "工号");
        head.put("name", "姓名");
        head.put("phone", "手机号");
        head.put("orgRankName", "组织机构");
        head.put("deptName", "归属部门");
        head.put("stateName", "状态");

        List<String> titles = new ArrayList(head.values());
        List<String> orders = new ArrayList(head.keySet());
        Workbook workbook = DefaultExcelBuilder
                .of(Map.class)
                .sheetName("sheet1")
                .titles(titles)
                .fieldDisplayOrder(orders)
                .build(list);
        ExportUtil.attachment(params, workbook, "用户信息");
    }

    /**
     * 退出登录
     *
     * @param params
     */
    @GetMapping("logout")
    public void logout(@RequestParam Map<String, Object> params) {
        log.debug("退出登录参数：{}", params);
        //更新退出时间
        String token = StpUtil.getTokenValue();
        params.put("token", token);
        Thread t = new Thread(() -> {
            try {
                this.daoHelper.insert("mapper.log.updateLogoutTime", params);
            } catch (Exception e) {
                //不做处理
                log.error("更新退出时间出错：{}", e.getMessage());
            }
        });
        t.start();
        StpUtil.logout();
    }

}

