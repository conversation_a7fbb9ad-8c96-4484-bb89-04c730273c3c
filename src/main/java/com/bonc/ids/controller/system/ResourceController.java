package com.bonc.ids.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.bonc.ids.annotation.Log;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.ResultEntity;
import com.bonc.ids.enums.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 资源管理
 */
@Slf4j
@RestController
@RequestMapping("/system/resource")
public class ResourceController {
    private DaoHelper daoHelper;
    private static final String XML_NAMESPACE = "mapper.resource.";

    public ResourceController(DaoHelper daoHelper) {
        this.daoHelper = daoHelper;
    }

    /**
     * 数据列表
     * @param params
     * @return
     */
    @GetMapping
    @SaCheckPermission("sys:res:view")
    public List<?> query(@RequestParam Map<String, Object> params) {
        log.debug("资源查询参数：{}", params);
        String statement = XML_NAMESPACE + "getList";
        List<?> list = this.daoHelper.queryForList(statement, params);
        return list;
    }

    /**
     * 保存
     * @param params
     * @return
     */
    @Log(title = "资源管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @SaCheckPermission(value = {"sys:res:add", "sys:res:edit"}, mode = SaMode.OR)
    public void save(@RequestBody Map<String, Object> params) {
        log.debug("资源保存参数：{}", params);
        String id = (String) params.get("id");
        String pId = (String) params.get("pId");
        String statement = "modify";
        if (StringUtils.isEmpty(id)) {
            params.put("id", UUID.randomUUID().toString());
            statement = "add";
        }
        if (StringUtils.isEmpty(pId)) {
            params.put("pId", "1");
        }
        this.daoHelper.update(XML_NAMESPACE + statement, params);
    }

    /**
     * 删除
     * @param params
     * @return
     */
    @Log(title = "资源管理", businessType = BusinessType.DELETE)
    @DeleteMapping
    @SaCheckPermission("sys:res:del")
    public void remove(@RequestBody Map<String, Object> params) {
        log.debug("资源删除参数: {}", params);
        this.daoHelper.update(XML_NAMESPACE + "delete", params);
    }

    /**
     * 上线、下线
     * @param params
     * @return
     */
    @Log(title = "资源管理", businessType = BusinessType.UPDATE)
    @PatchMapping
    @SaCheckPermission(value = {"sys:res:online", "sys:res:offline"}, mode = SaMode.OR)
    public void patch(@RequestBody Map<String, Object> params) {
        log.debug("资源更新参数: {}", params);
        this.daoHelper.update(XML_NAMESPACE + "update", params);
    }

    /**
     * 权限标识可用性校验
     * @param params
     * @return
     */
    @GetMapping("valid")
    public ResultEntity validAuthCode(@RequestParam Map<String, Object> params) {
        log.debug("资源可用性校验参数：{}", params);
        int flag = this.daoHelper.queryForObject(XML_NAMESPACE + "checkValid", params);
        if (flag > 0) {
            return ResultEntity.error("标识不可用");
        }
        return ResultEntity.success("标识可用");
    }
}
