package com.bonc.ids.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.bonc.ids.annotation.Log;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.ResultEntity;
import com.bonc.ids.enums.BusinessType;
import com.bonc.ids.utils.ConvertUtil;
import com.bonc.ids.utils.PageUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @create 2020-08-11 9:51
 * @discription 角色管理
 */
@Slf4j
@RestController
@RequestMapping("/system/role")
public class RoleController {
    private Da<PERSON><PERSON><PERSON><PERSON> daoHelper;

    public RoleController(<PERSON><PERSON><PERSON><PERSON><PERSON> daoHelper) {
        this.daoHelper = daoHelper;
    }

    /**
     * 角色列表
     * @param params
     * @return
     */
    @GetMapping
    @SaCheckPermission("sys:role:view")
    public List<?> query(@RequestParam Map<String, Object> params) {
        log.debug("角色查询参数：{}", params);
        List<?> list = this.daoHelper.queryForList("mapper.role.getList", params);
        return list;
    }

    /**
     * 保存数据
     * @param params
     * @return
     */
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @SaCheckPermission(value = {"sys:role:add", "sys:role:edit"}, mode = SaMode.OR)
    public void save(@RequestBody Map<String, Object> params) {
        log.debug("角色保存参数: {}", params);
        String id = (String) params.get("id");
        String statement = "modify";
        if (StringUtils.isEmpty(id)) {
            params.put("id", UUID.randomUUID().toString());
            statement = "add";
        }
        this.daoHelper.update("mapper.role." + statement, params);
    }

    /**
     * 禁用、启用
     * @param params
     * @return
     */
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PatchMapping
    @SaCheckPermission(value = {"sys:role:disable", "sys:role:disable"}, mode = SaMode.OR)
    public ResultEntity forbid(@RequestBody Map<String, Object> params) {
        log.debug("角色更新参数：{}", params);
        int state = ConvertUtil.toInt(params.get("state"));
        //如果是启用操作，先判断父级节点的状态是否为启用，否则提示用户先启用父级
        if (state == 1) {
            int parentState = this.daoHelper.queryForObject("mapper.role.getParentState", params);
            if (parentState == 0) {
                return ResultEntity.error("请先启用父级角色");
            }
        }
        this.daoHelper.update("mapper.role.forbid", params);
        return ResultEntity.success("操作成功");
    }

    /**
     * 删除数据
     * @param params
     * @return
     */
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping
    @SaCheckPermission("sys:role:del")
    public void remove(@RequestBody Map<String, Object> params) {
        log.debug("角色删除参数：{}", params);
        this.daoHelper.update("mapper.role.delete", params);
    }

    /**
     * 查询角色标识是否存在
     * @param params
     * @return
     */
    @GetMapping("auth-code")
    public ResultEntity validAuthCode(@RequestParam Map<String, Object> params) {
        log.debug("查询角色标识是否存在参数：{}", params);
        int result = this.daoHelper.queryForObject("mapper.role.checkAuthCode", params);
        if (result == 0) {
            return ResultEntity.success("标识可用");
        }
        return ResultEntity.error("标识已存在");
    }

    /**
     * 查询可授权或回收的用户列表
     * @param params
     * @return
     */
    @GetMapping("users")
    public PageInfo<?> users(@RequestParam Map<String, Object> params) {
        log.debug("可授权或回收用户查询参数：{}", params);
        PageUtil.startPage(params);
        PageInfo<?> pageInfo = this.daoHelper.queryForPageList("mapper.role.getUsers", params);
        return pageInfo;
    }

    /**
     * 保存用户授权
     * @param params
     * @return
     */
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("users")
    @SaCheckPermission(value = {"sys:role:grant:user", "sys:role:revoke:user"}, mode = SaMode.OR)
    public void saveUsers(@RequestBody Map<String, Object> params) {
        log.debug("保存用户授权参数：{}", params);
        this.daoHelper.update("mapper.role.saveUsers", params);
    }

    /**
     * 查询可授权资源
     * @param params
     * @return
     */
    @GetMapping("resources")
    public List<?> resources(@RequestParam Map<String, Object> params) {
        log.debug("可授权资源查询参数：{}", params);
        List<?> list = this.daoHelper.queryForList("mapper.role.getResources", params);
        return list;
    }

    /**
     * 保存资源授权
     * @param params
     * @return
     */
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("resources")
    @SaCheckPermission("sys:role:grant:res")
    public void saveResources(@RequestBody Map<String, Object> params) {
        log.debug("保存资源授权参数：{}", params);
        this.daoHelper.update("mapper.role.saveResources", params);
    }
}
