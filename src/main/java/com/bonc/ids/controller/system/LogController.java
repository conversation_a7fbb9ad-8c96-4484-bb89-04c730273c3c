package com.bonc.ids.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bonc.ids.annotation.Log;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.enums.BusinessType;
import com.bonc.ids.utils.ExportUtil;
import com.bonc.ids.utils.PageUtil;
import com.bonc.ids.utils.SpringContextUtil;
import com.github.liaochong.myexcel.core.DefaultExcelBuilder;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2020-08-11 9:51
 * @discription 日志管理
 */
@Slf4j
@RestController
@RequestMapping("/system/logger")
public class LogController {
    private static final String XML_NAMESPACE = "mapper.log.";
    private DaoHelper daoHelper;

    public LogController(<PERSON><PERSON><PERSON><PERSON><PERSON> daoHelper) {
        this.daoHelper = daoHelper;
    }

    /**
     * 登录日志
     * @param params
     * @return
     */
    @GetMapping("login")
    @SaCheckPermission("sys:log:login:view")
    public PageInfo<?> login(@RequestParam Map<String, Object> params) {
        log.debug("登录日志查询参数：{}", params);
        PageUtil.startPage(params);
        PageInfo<?> pageInfo = this.daoHelper.queryForPageList(XML_NAMESPACE + "getLoginList", params);
        return pageInfo;
    }

    /**
     * 登录日志导出
     * @param params
     */
    @Log(title = "日志管理 - 登录日志", businessType = BusinessType.EXPORT)
    @PostMapping("login")
    @SaCheckPermission("sys:log:login:export")
    public void exportLogin(@RequestBody Map<String, Object> params) {
        log.debug("登录日志导出参数：{}", params);
        List<Map> list = this.daoHelper.queryForList(XML_NAMESPACE + "getLoginList", params);
        Map<String, String> head = new LinkedHashMap<>();
        head.put("account", "账号");
        head.put("name", "姓名");
        head.put("departmentName", "归属部门");
        head.put("orgName", "组织机构");
        head.put("clientIp", "登录IP");
        head.put("loginTime", "登录时间");
        head.put("loginSource", "登录源");
        head.put("memo", "登录结果");

        List<String> titles = new ArrayList(head.values());
        List<String> orders = new ArrayList(head.keySet());
        Workbook workbook = DefaultExcelBuilder
                .of(Map.class)
                .sheetName("sheet1")
                .titles(titles)
                .fieldDisplayOrder(orders)
                .build(list);
        ExportUtil.attachment(params, workbook, "登录日志");
    }

    /**
     * 访问日志
     * @param params
     * @return
     */
    @GetMapping("visit")
    @SaCheckPermission("sys:log:visit:view")
    public PageInfo<?> visit(@RequestParam Map<String, Object> params) {
        log.debug("访问日志查询参数：{}", params);
        PageUtil.startPage(params);
        PageInfo<?> pageInfo = this.daoHelper.queryForPageList(XML_NAMESPACE + "getVisitList", params);
        return pageInfo;
    }

    /**
     * 保存访问日志
     * @param params
     */
    @PutMapping("visit")
    public void saveVisit(@RequestBody Map<String, Object> params) {
        log.debug("访问日志记录参数：{}", params);
        String profile = SpringContextUtil.getActiveProfile();
        if (!"prod".equalsIgnoreCase(profile)) {
            log.debug("非生产环境，不记录日志");
            return;
        }
        Thread t = new Thread(() -> {
            try {
                params.put("id", UUID.randomUUID().toString());
                this.daoHelper.insert("mapper.common.saveVisitLog", params);
            } catch (Exception e) {
                //不做处理
                log.error("访问日志记录出错：{}", e.getMessage());
            }
        });
        t.start();
    }

    /**
     * 访问日志导出
     * @param params
     */
    @Log(title = "日志管理 - 访问日志", businessType = BusinessType.EXPORT)
    @PostMapping("visit")
    @SaCheckPermission("sys:log:visit:export")
    public void exportVisit(@RequestBody Map<String, Object> params) {
        log.debug("访问日志导出参数：{}", params);

        List<Map> list = this.daoHelper.queryForList(XML_NAMESPACE + "getVisitList", params);
        Map<String, String> head = new LinkedHashMap<>();
        head.put("page", "模块名称");
        head.put("account", "访问账号");
        head.put("name", "访问人");
        head.put("departmentName", "归属部门");
        head.put("orgName", "组织机构");
        head.put("source", "访问来源");
        head.put("visitTime", "访问时间");
        head.put("duration", "停留时长（秒）");

        List<String> titles = new ArrayList(head.values());
        List<String> orders = new ArrayList(head.keySet());
        Workbook workbook = DefaultExcelBuilder
                .of(Map.class)
                .sheetName("sheet1")
                .titles(titles)
                .fieldDisplayOrder(orders)
                .build(list);
        ExportUtil.attachment(params, workbook, "访问日志");
    }

    /**
     * 数据上传维护日志访问日志
     * @param params
     * @return
     */
    @GetMapping("upload")
    @SaCheckPermission("sys:log:import:view")
    public PageInfo<?> upload(@RequestParam Map<String, Object> params) {
        log.debug("访问日志查询参数：{}", params);
        PageUtil.startPage(params);
        PageInfo<?> pageInfo = this.daoHelper.queryForPageList(XML_NAMESPACE + "getUploadList", params);
        return pageInfo;
    }
    /**
     * 数据上传维护日志导出
     * @param params
     */
    @Log(title = "日志管理 - 导入日志", businessType = BusinessType.EXPORT)
    @PostMapping("uploadexport")
    @SaCheckPermission("sys:log:import:export")
    public void uploadExport(@RequestBody Map<String, Object> params) {
        log.debug("访问日志导出参数：{}", params);

        List<Map> list = this.daoHelper.queryForList(XML_NAMESPACE + "getUploadList", params);
        Map<String, String> head = new LinkedHashMap<>();
        head.put("page", "模块名称");
        head.put("import_user", "访问账号");
        head.put("name", "访问人");
        head.put("departmentName", "归属部门");
        head.put("orgName", "组织机构");
        head.put("fileDate", "上传账期");
        head.put("tableName", "上传表");
        head.put("tableDesc", "上传表注释");
        head.put("importTime", "上传时间");
        head.put("dataCount", "上传记录数");
        head.put("impReSult", "上传结果");
        

        List<String> titles = new ArrayList(head.values());
        List<String> orders = new ArrayList(head.keySet());
        Workbook workbook = DefaultExcelBuilder
                .of(Map.class)
                .sheetName("sheet1")
                .titles(titles)
                .fieldDisplayOrder(orders)
                .build(list);
        ExportUtil.attachment(params, workbook, "访问日志");
    }
    /**
     * 访问排名（暂时去掉排名，脚本容易导致RDS占用过高）
     * @param params
     * @return
     */
    @GetMapping("rank")
    @SaCheckPermission("sys:log:visit:rank:view")
    public PageInfo<?> rank(@RequestParam Map<String, Object> params) {
        log.debug("访问排名查询参数：{}", params);
//        PageUtil.startPage(params);
//        PageInfo<?> pageInfo = this.daoHelper.queryForPageList(XML_NAMESPACE + "getRankList", params);
//        return pageInfo;
        return null;
    }

    /**
     * 访问排名导出
     * @param params
     */
    @Log(title = "日志管理 - 访问排名", businessType = BusinessType.EXPORT)
    @PostMapping("rank")
    @SaCheckPermission("sys:log:visit:rank:export")
    public void exportRank(@RequestBody Map<String, Object> params) {
        log.debug("访问排名导出参数：{}", params);

        List<Map> list = this.daoHelper.queryForList(XML_NAMESPACE + "getRankList", params);
        Map<String, String> head = new LinkedHashMap<>();
        head.put("page", "模块名称");
        head.put("count", "访问次数");
        head.put("visitTime", "最后访问时间");
        head.put("account", "最后访问账号");
        head.put("name", "最后访问人");

        List<String> titles = new ArrayList(head.values());
        List<String> orders = new ArrayList(head.keySet());
        Workbook workbook = DefaultExcelBuilder
                .of(Map.class)
                .sheetName("sheet1")
                .titles(titles)
                .fieldDisplayOrder(orders)
                .build(list);
        ExportUtil.attachment(params, workbook, "访问排名");
    }

    /**
     * 导出日志
     * @param params
     * @return
     */
    @GetMapping("export")
    @SaCheckPermission("sys:log:export:view")
    public PageInfo<?> export(@RequestParam Map<String, Object> params) {
        log.debug("导出日志查询参数：{}", params);
        PageUtil.startPage(params);
        PageInfo<?> pageInfo = this.daoHelper.queryForPageList(XML_NAMESPACE + "getExportList", params);
        return pageInfo;
    }

    /**
     * 导出日志导出
     * @param params
     */
    @Log(title = "日志管理 - 导出日志", businessType = BusinessType.EXPORT)
    @PostMapping("export")
    @SaCheckPermission("sys:log:export:export")
    public void exportLog(@RequestBody Map<String, Object> params) {
        log.debug("导出日志导出参数：{}", params);

        List<Map> list = this.daoHelper.queryForList(XML_NAMESPACE + "getExportList", params);
        Map<String, String> head = new LinkedHashMap<>();
        head.put("page", "模块名称");
        head.put("count", "导出记录数");
        head.put("exportTime", "导出时间");
        head.put("account", "导出账号");
        head.put("name", "导出人");
        head.put("departmentName", "归属部门");
        head.put("orgName", "组织机构");

        List<String> titles = new ArrayList(head.values());
        List<String> orders = new ArrayList(head.keySet());
        Workbook workbook = DefaultExcelBuilder
                .of(Map.class)
                .sheetName("sheet1")
                .titles(titles)
                .fieldDisplayOrder(orders)
                .build(list);
        ExportUtil.attachment(params, workbook, "导出日志");
    }

    /**
     * 操作日志列表
     * @param params
     * @return
     */
    @GetMapping("operate")
    @SaCheckPermission("sys:log:operate:view")
    public PageInfo<?> queryOperateLog(@RequestParam Map<String, Object> params) {
        log.debug("操作日志查询参数：{}", params);
        PageUtil.startPage(params);
        PageInfo<?> pageInfo = this.daoHelper.queryForPageList(XML_NAMESPACE + "getOperateList", params);
        return pageInfo;
    }
}
