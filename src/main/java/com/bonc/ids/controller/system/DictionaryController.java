package com.bonc.ids.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.bonc.ids.annotation.Log;
import com.bonc.ids.dao.DaoHelper;
import com.bonc.ids.entity.ResultEntity;
import com.bonc.ids.enums.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-12-07 10:29
 * @description 字典管理
 */
@Slf4j
@RestController
@RequestMapping("/system/dictionary")
public class DictionaryController {
    private DaoHelper daoHelper;

    public DictionaryController(DaoHelper daoHelper) {
        this.daoHelper = daoHelper;
    }

    /**
     * 数据列表
     * @param params
     * @return
     */
    @GetMapping
    @SaCheckPermission(value = {"sys:dict:view", "bonc:demand:view"}, mode = SaMode.OR)
    public List<?> queryList(@RequestParam Map<String, Object> params) {
        log.debug("字典列表查询参数：{}", params);
        List<?> list = this.daoHelper.queryForList("mysql.system.dictionary.getList", params);
        return list;
    }

    /**
     * 保存数据
     * @param params
     * @return
     */
    @SaCheckPermission(value = {"sys:dict:add", "sys:dict:edit"}, mode = SaMode.OR)
    @Log(title = "字典管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ResultEntity save(@RequestBody Map<String, Object> params) {
        log.debug("字典保存参数：{}", params);
        String statement = "update";
        String option = (String) params.get("option");
        String pId = (String) params.get("pId");
        if (StringUtils.isEmpty(option) || "create,update".indexOf(option) == -1) {
            return ResultEntity.error("错误操作");
        }
        if (StringUtils.isEmpty(pId)) {
            params.put("pId", "root");
        }
        if ("create".equalsIgnoreCase(option)) {
            statement = "insert";
        }
        this.daoHelper.update("mysql.system.dictionary." + statement, params);
        return ResultEntity.success("保存成功");
    }

    /**
     * 校验编码是否可用
     * @param params
     * @return
     */
    @SaCheckPermission(value = {"sys:dict:add", "sys:dict:edit"}, mode = SaMode.OR)
    @PatchMapping
    public ResultEntity valid(@RequestBody Map<String, Object> params) {
        log.debug("字典编码校验参数：{}", params);
        int flag = this.daoHelper.queryForObject("mysql.system.dictionary.valid", params);
        if (flag == 0) {
            return ResultEntity.success("编码可用");
        }
        return ResultEntity.error("编码已存在");
    }

    /**
     * 删除数据
     * @param params
     * @return
     */
    @SaCheckPermission("sys:dict:del")
    @Log(title = "字典管理", businessType = BusinessType.DELETE)
    @DeleteMapping
    public void remove(@RequestBody Map<String, Object> params) {
        log.debug("字典删除参数：{}", params);
        this.daoHelper.delete("mysql.system.dictionary.delete", params);
    }
}
