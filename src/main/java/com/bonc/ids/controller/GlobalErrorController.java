package com.bonc.ids.controller;

import com.bonc.ids.entity.ResultEntity;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description 全局错误统一返回
 * <AUTHOR>
 * @Date 2021/11/8 16:41
 */
@RestController
@RequestMapping("/error")
public class GlobalErrorController implements ErrorController {

    @GetMapping
    public ResultEntity error(HttpServletResponse response) {
        return ResultEntity.error("请求出错，错误代码：" + response.getStatus());
    }
}
