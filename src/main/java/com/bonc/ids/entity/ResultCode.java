package com.bonc.ids.entity;

/**
 * <AUTHOR>
 * @create 2022-04-02 14:46
 * @description 返回状态码
 */
public enum ResultCode {
    /**
     * 成功
     */
    SUCCESS(200),
    /**
     * 失败
     */
    FAIL(999),
    /**
     * 未认证（签名错误）
     */
    UNAUTHORIZED(401),
    /**
     * 接口不存在
     */
    NOT_FOUND(404),

    /**
     * 认证过期
     */
    AUTH_EXPIRED(10001),

    /**
     * 服务器内部错误
     */
    INTERNAL_SERVER_ERROR(500);

    public int code;

    ResultCode(int code) {
        this.code = code;
    }
}
