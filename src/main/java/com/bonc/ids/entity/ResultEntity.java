package com.bonc.ids.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020-08-10 12:44
 * @discription 请求返回的消息实体
 */
@Data
public class ResultEntity<T> {
    private int code;
    private String msg;
    private T data;
    private long timestamp = System.currentTimeMillis();

    public ResultEntity() {}

    public ResultEntity(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResultEntity(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> ResultEntity<T> success(String msg, T data) {
        return new ResultEntity(ResultCode.SUCCESS.code, msg, data);
    }

    public static <T> ResultEntity<T> success(T data) {
        return success("操作成功", data);
    }

    public static ResultEntity success(String msg) {
        return success(msg, null);
    }

    public static ResultEntity error(String msg) {
        return new ResultEntity(ResultCode.FAIL.code, msg);
    }

    /**
     * 是否返回成功
     * @return
     */
    public boolean isSuccess() {
        return this.code == ResultCode.SUCCESS.code;
    }
}
