package com.bonc.ids.entity.system;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.core.ResolvableType;
import org.springframework.core.ResolvableTypeProvider;

/**
 * <AUTHOR>
 * @create 2024-01-16 10:51
 * @description 通用事件实体类
 */
@Data
@RequiredArgsConstructor
public class BaseEvent<T> implements ResolvableTypeProvider {
    private final T entity;

    @Override
    public ResolvableType getResolvableType() {
        return ResolvableType.forClassWithGenerics(getClass(), ResolvableType.forInstance(getEntity()));
    }
}
