package com.bonc.ids.entity.system.log;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-12-14 10:53
 * @description 操作日志实体类
 */
@Data
public class OperateLog {
    /**
     * 日志ID
     */
    private Long id;

    /**
     * 操作模块
     */
    private String title;

    /**
     * 业务类型 BusinessType
     */
    private Integer businessType;

    /**
     * 请求方法
     */
    private String func;

    /**
     * 请求方式
     */
    private String method;

    /**
     * 操作人
     */
    private String userId;

    /**
     * 请求url
     */
    private String url;

    /**
     * 操作地址
     */
    private String ip;

    /**
     * 操作地点
     */
    private String location;

    /**
     * 请求参数
     */
    private String params;

    /**
     * 返回参数
     */
    private String result;

    /**
     * 操作状态（0正常 1异常）
     */
    private Integer status;

    /**
     * 请求结果备注
     */
    private String memo;

    /**
     * 消耗时间
     */
    private Long costTime;
}
