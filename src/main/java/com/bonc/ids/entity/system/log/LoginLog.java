package com.bonc.ids.entity.system.log;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-12-14 15:34
 * @description 登录日志实体类
 */
@Data
public class LoginLog {
    /**
     * 日志ID
     */
    private String id;

    /**
     * 登录账号
     */
    private String account;

    /**
     * 认证token
     */
    private String token;

    /**
     * 登录结果，1：成功，0：失败
     */
    private int success;

    /**
     * 客户端IP
     */
    private String ip;

    /**
     * IP归属地
     */
    private String location;

    /**
     * 浏览器
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 登录源
     */
    private String signType = "SYS";

    /**
     * 退出时间
     */
    private Date logoutTime;

    /**
     * 备注
     */
    private String memo;
}
