package com.bonc.ids.dao.impl;

import com.bonc.ids.dao.DaoHelper;
import com.github.pagehelper.PageInfo;

import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.support.SqlSessionDaoSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2020-08-10 12:10
 * @discription
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class DaoHelperImpl extends SqlSessionDaoSupport implements <PERSON>o<PERSON>elper {
    @Autowired
    public void setSqlSessionFactory(SqlSessionFactory sqlSessionFactory){
        super.setSqlSessionFactory(sqlSessionFactory);
    }

    /**
     * 多记录查询
     * @param statement
     * @param parameter
     * @return
     */
    public <E> List<E> queryForList(String statement, Object parameter) {
        return getSqlSession().selectList(statement, parameter);
    }

    /**
     * 多记录分页查询
     * @param statement
     * @param parameter
     * @return
     */
    public <E> PageInfo<E> queryForPageList(String statement, Object parameter) {
        List<E> list = getSqlSession().selectList(statement, parameter);
        PageInfo<E> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

    /**
     * 查询总记录数
     * @param statement
     * @param parameter
     * @return
     */
    public int queryForTotalRows(String statement, Object parameter) {
        List<?> list = getSqlSession().selectList(statement, parameter);
        return list.size();
    }

    /**
     * 查询Map对象
     * @param statement
     * @param parameter
     * @param mapKey
     * @return
     */
    public <K, V> Map<K, V> queryForMap(String statement, Object parameter, String mapKey) {
        return getSqlSession().selectMap(statement, parameter, mapKey);
    }

    /**
     * 单记录查询
     * @param statement
     * @param parameter
     * @return
     */
    public <T> T queryForObject(String statement, Object parameter) {
        return getSqlSession().selectOne(statement, parameter);
    }

    /**
     * 更新
     * @param statement
     * @param parameter
     * @return 受影响记录数
     */
    public int update(String statement, Object parameter) {
        return getSqlSession().update(statement, parameter);
    }

    /**
     * 插入
     * @param statement
     * @param parameter
     * @return 受影响记录数s
     */
    public int insert(String statement, Object parameter) {
        return getSqlSession().insert(statement, parameter);
    }

    /**
     * 删除
     * @param statement
     * @param parameter
     * @return 受影响记录数
     */
    public int delete(String statement, Object parameter) {
        return getSqlSession().delete(statement, parameter);
    }
    /**
     * 批量写入
     * @param statement
     * @param list
     * @return
     */
    @Async
    public <T> CompletableFuture<Integer> saveBatch(String statement, List<T> list, int batch) {
        SqlSession session = getSqlSessionFactory().openSession(ExecutorType.BATCH, false);
        try {
            int size = list.size();
            for (int i = 0; i < size; i++) {
                session.insert(statement, list.get(i));
                if (i % batch == 0 || (i + 1) == size) {
                    session.flushStatements();
                }
            }
            session.commit();
            return new AsyncResult<>(1).completable();
        } catch (Exception e) {
            session.rollback();
            session.clearCache();
        } finally {
            session.close();
        }
        return new AsyncResult<>(0).completable();
    }
}
