package com.bonc.ids.dao;

import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2020-08-10 12:09
 * @discription
 */
public interface DaoHelper {
    <E> List<E> queryForList(String statement, Object parameter);
    <E> PageInfo<E> queryForPageList(String statement, Object parameter);
    int queryForTotalRows(String statement, Object parameter);
    <K, V> Map<K, V> queryForMap(String statement, Object parameter, String mapKey);
    <T> T queryForObject(String statement, Object parameter);
    int update(String statement, Object parameter);
    int insert(String statement, Object parameter);
    int delete(String statement, Object parameter);
    <T> CompletableFuture<Integer> saveBatch(String statement, List<T> list, int batch);
}
