# 服务端口
server:
  port: 8094
  servlet:
    context-path: /ids-server

app:
  # 必须以/结尾
  attachment: d:/attachment/

  # 对象存储COS
  cos-key: WQ0E7EJMF108IK8849AD
  cos-secret: 151562
  cos-endpoint: cos.hhht-hqc.cos.tg.unicom.local
  cos-region: hhht-hqc


spring:
  # 数据源配置
  datasource:
    # druid数据源配置
    druid:
      # 主数据源（OceanBase oracle模式）
      master:
        driver-class-name: com.oceanbase.jdbc.Driver
        url: *********************************************,10.125.32.35:2883,10.125.32.127:2883/?socketTimeout=60000000&connectTimeout=60000&autoReconnect=true&rewriteBatchedStatements=true&allowMultiQueries=true&useServerPrepStmts=false
        username: IOSS@ioss_ts#obdemo6
        password: IOss_ts#186
  # Redis 配置（无锡测试区）
  redis:
    # 数据库索引（默认为0）
    database: 1
    # 密码
    password: 'IOss#186'
    # 连接超时时间
    timeout: 10000
    #lettuce:
    lettuce:
      pool:
        max-wait: 1000ms
    connect-timeout: 5000
    pool:
      # 连接池中的最小空闲连接
      min-idle: 0
      # 连接池中的最大空闲连接
      max-idle: 8
      # 连接池的最大数据库连接数
      max-active: 8
      # #连接池最大阻塞等待时间（使用负值表示没有限制）
      max-wait: -1ms
    sentinel:
      master: ioss-ts-redis
      nodes:
        - 10.125.176.66:32518
        - 10.125.176.73:32507
        - 10.125.176.75:32524

sa-token:
  active-timeout: -1
