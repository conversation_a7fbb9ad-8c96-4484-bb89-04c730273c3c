# 应用配置（以下为生产配置，勿动，需要修改请在dev的配置文件中覆盖）
app:
  # 必须以/结尾
  attachment: /data/attachments/
  # 部分数据加密用到的Key（固定16位长度）
  cipher-key: 9fjnFfj3rlV$&u6%
  # Token加密Key（256位）
  token-key: PMVHV&IlmzOV@wRjgXDAM0c#L5dsQXNfvjGu5C#bsQvyL$eqU7&5YNiLGQ7Im*Ko
  # Token有效期（默认1天，单位：秒）
  token-expire: 86400
  # 是否启用密码失效校验
  enable-password-expire: false
  # 密码有效期（单位：天）
  password-expire: 90

  # 对象存储COS
  cos-key: WQ0E7EJMF108IK8849AD
  cos-secret: 111
  cos-endpoint: cos.hhht-hqc.cos.tg.unicom.local
  cos-region: hhht-hqc


# 开发模式
spring:
  profiles:
    active: dev
  # 上传文件大小配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 500MB

# 日志
logging:
  file:
    path: ./logs
  config: classpath:logback-${spring.profiles.active}.xml


############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: "IDS-Authorization"
  # token 有效期（单位：秒） 默认1天，-1 代表永久有效
  timeout: 86400
  # token最低活跃时间 (指定时间无操作就过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # 是否尝试从cookie里读取token
  is-read-cookie: false
  # 是否打印版本字符
  is-print: false
  # jwt秘钥
  jwt-secret-key: 123321123321
