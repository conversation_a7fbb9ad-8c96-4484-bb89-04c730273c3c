server:
  # 服务端口
  port: 8064
  servlet:
    context-path: /ids-server


app:
  # 导出
  export:
    # 是否开启导出功能
    enable: true

spring:
  # 数据源配置
  datasource:
    druid:
      # 主数据源（OceanBase oracle模式）
      master:
        driver-class-name: com.oceanbase.jdbc.Driver
        url: ***********************************************,10.129.74.179:3306,10.129.74.184:3306/?socketTimeout=60000000&connectTimeout=60000&autoReconnect=true&rewriteBatchedStatements=true&allowMultiQueries=true&useServerPrepStmts=false
        username: IOSS@ioss#hl_cluster10
        password: IOss_ts#186
  # Redis 配置（无锡省分生产区）
  redis:
    # 数据库索引（默认为0）
    database: 1
    # 密码
    password: 'IOss#186'
    # 连接超时时间
    timeout: 10000
    #lettuce:
    lettuce:
      pool:
        max-wait: 1000ms
    connect-timeout: 5000
    pool:
      # 连接池中的最小空闲连接
      min-idle: 0
      # 连接池中的最大空闲连接
      max-idle: 8
      # 连接池的最大数据库连接数
      max-active: 8
      # #连接池最大阻塞等待时间（使用负值表示没有限制）
      max-wait: -1ms
    cluster:
      nodes:
        - 10.188.101.152:32722
        - 10.188.101.151:32720
        - 10.188.205.138:32521
        - 10.188.205.141:32519
        - 10.188.205.124:32517
        - 10.188.205.140:32518
      max-redirects: 3
