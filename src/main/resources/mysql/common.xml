<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.common">
    <!--记录导出日志-->
    <insert id="saveExportLog">
        INSERT INTO ioss.sys_export_log ( id, account, page_name, page_path, export_count )
        VALUES
        (
            #{id, jdbcType=VARCHAR},
            #{__ACCOUNT, jdbcType=VARCHAR},
            #{__PAGE_NAME, jdbcType=VARCHAR},
            #{__PAGE_PATH, jdbcType=VARCHAR},
            #{count, jdbcType=VARCHAR}
        )
    </insert>

    <!--记录访问日志-->
    <insert id="saveVisitLog">
        <if test="pagePath != beforePath">
            INSERT INTO ioss.sys_visit_log ( id, visit_user, page_path, full_path, page_name, env )
            VALUES
            (
                #{pageId, jdbcType=VARCHAR},
                #{__ACCOUNT, jdbcType=VARCHAR},
                #{pagePath, jdbcType=VARCHAR},
                #{fullPath, jdbcType=VARCHAR},
                #{pageName, jdbcType=VARCHAR},
                #{env, jdbcType=VARCHAR}
            );
        </if>
        UPDATE ioss.sys_visit_log
        SET end_time = SYSDATE
        WHERE
            id = #{prevPageId, jdbcType=VARCHAR};
    </insert>

    <!--记录数据导入日志-->
    <insert id="saveImportLog">
        INSERT INTO ioss.sys_import_log ( id, page_name, page_path, file_name, file_size, data_count, import_user ,TABLE_NAME,IMP_RESULT,FILE_DATE,TABLE_DESC,
        RESULT_DESC
        )
        VALUES
        (
            #{id, jdbcType=VARCHAR},
            #{__PAGE_NAME, jdbcType=VARCHAR},
            #{__PAGE_PATH, jdbcType=VARCHAR},
            #{filename, jdbcType=VARCHAR},
            #{fileSize, jdbcType=NUMERIC},
            #{dataCount, jdbcType=NUMERIC},
            #{__ACCOUNT, jdbcType=VARCHAR},
            #{tableName, jdbcType=VARCHAR},
            NVL(#{impResult, jdbcType=VARCHAR}, 'SUCESS'),
            #{fileDate, jdbcType=VARCHAR},
            #{tableDesc, jdbcType=VARCHAR},
            #{resultDesc, jdbcType=VARCHAR}
        )
    </insert>

    <!--记录查询日志（用户ID查询号码功能）-->
    <insert id="saveUserIdQueryLog">
        INSERT INTO ioss.sys_device_number_query_log ( id, user_id, device_number, create_user )
        VALUES
            (
                #{id, jdbcType=VARCHAR},
                #{userId, jdbcType=VARCHAR},
                #{deviceNumber, jdbcType=VARCHAR},
                #{__ACCOUNT, jdbcType=VARCHAR})
    </insert>

    <!--获取当前登录用户手机号-->
    <select id="getPhoneNumber" resultType="string">
        SELECT phone FROM uac.sys_user WHERE id = #{__OPERATOR, jdbcType=VARCHAR}
    </select>

    <!--发送解压密码-->
    <insert id="sendExtractPassword">
        INSERT INTO DM.DM_SMS_MONITOR
          (ID, DEVICE_NUMBER, CONTENT, TOPIC, PERSON, BUSI_TYPE, INSERT_TIME)
        VALUES
          (DM.SEQ_DM_SMS_MONITOR.NEXTVAL,
           #{phone, jdbcType=VARCHAR},
           '你的文件访问密码：' || #{password, jdbcType=VARCHAR} || '，请勿外传. [本短信由系统自动发送，无需回复]',
           '反诈系统导出文件密码',
           NVL(#{__OPERATOR, jdbcType=VARCHAR}, 'SYSADMIN'),
           'ANTI-FRAUD-PC',
           SYSDATE)
    </insert>

    <!--保存附件信息-->
    <insert id="saveAttachment">
        INSERT INTO SYS_ATTACHMENTS
          (ID, NAME, ORIGINAL_NAME, EXTENSION, FILE_PATH, MIME_TYPE, CREATE_USER)
        VALUES
          (#{id, jdbcType=VARCHAR},
           #{fileName, jdbcType=VARCHAR},
           #{originalName, jdbcType=VARCHAR},
           #{fileExtension, jdbcType=VARCHAR},
           #{filePath, jdbcType=VARCHAR},
           #{mimeType, jdbcType=VARCHAR},
           #{__OPERATOR, jdbcType=VARCHAR})
    </insert>

    <!--根据表名查询日志表中的最大可用账期-->
    <select id="getMaxCycle" resultType="string">
        SELECT
            MAX(date_id)
        FROM
            ids.sys_data_cycles
        WHERE
            table_name = #{tableName, jdbcType=VARCHAR}
    </select>

</mapper>
