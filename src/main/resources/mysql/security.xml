<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.security">

    <!--用户信息-->
    <resultMap id="userResultMap" type="hashmap">
        <collection property="resources" javaType="arraylist" column="{id=id}" select="getResources" />
        <collection property="roles" javaType="arraylist" column="{id=id}" select="getRoles" />
    </resultMap>
    <select id="getUserByAccount" resultMap="userResultMap">
        SELECT
            id "id",
            account "account",
            name "name",
            password "password",
            gender "gender",
            CASE
                WHEN gender = 1 THEN
                '男'
                WHEN gender = 2 THEN
                '女' ELSE '未知'
            END "genderDesc",
            phone "phone",
            email "email",
            dept_id "deptId",
            c.org_id "orgId",
            state "state",
            CASE
                WHEN b.user_id IS NOT NULL THEN
                1 ELSE a.is_admin
            END "isAdmin",
            salt "salt"
        FROM
            uac.sys_user a
            LEFT JOIN (
            SELECT DISTINCT
                b.user_id
            FROM
                ioss.sys_roles a
                JOIN ioss.sys_user_role b ON a.id = b.role_id
            WHERE
                a.state = 1
                AND a.id = '1'
            ) b ON a.id = b.user_id
        left join uac.sys_user_org c on a.id = c.user_id
        WHERE
            a.account = #{account, jdbcType=VARCHAR}
        FETCH FIRST 1 ROW ONLY
    </select>

    <!--已授权的资源-->
    <select id="getResources" resultType="hashmap">
        SELECT DISTINCT
            pid "pId",
            id "id",
            CASE
             WHEN TYPE = 2 AND IS_FRAME = 1 AND OPEN_TYPE = 'iframe' THEN
              CONCAT('/external/frame/', ID)
             ELSE
              PATH
           END "path",
            path "originPath",
            type "type",
            odr "odr",
            name "name",
            show "show",
            icon "icon",
            need_token "needToken",
            watermark "watermark",
            breadcrumb "breadcrumb",
            NVL(auth_code, id) "authCode"
        FROM
            ioss.sys_resources t1
            JOIN (
            SELECT DISTINCT
                resource_id
            FROM
                (
                SELECT
                    c.resource_id
                FROM
                    ioss.sys_user_role a
                    JOIN ioss.sys_roles b ON a.role_id = b.id
                    JOIN ioss.sys_role_resource c ON a.role_id = c.role_id
                WHERE
                    b.state = 1
                    AND a.user_id = #{id, jdbcType=VARCHAR} UNION ALL
                SELECT
                    resource_id
                FROM
                    ioss.sys_user_resource
                WHERE
                    user_id = #{id, jdbcType=VARCHAR}
                ) t
            ) t2 ON t1.id = t2.resource_id
        WHERE
            t1.state = 1
            AND t1.platform = 'IDS'
        ORDER BY
            odr
    </select>

    <!--用户所拥有的角色-->
    <select id="getRoles" resultType="string">
        SELECT DISTINCT
            NVL(a.auth_code, a.id)
        FROM
            ioss.sys_roles a
            JOIN ioss.sys_user_role b ON a.id = b.role_id
        WHERE
            a.state = 1
            AND b.user_id = #{id, jdbcType=VARCHAR}
    </select>

    <!--存储登录页面的图形验证码-->
    <insert id="saveImageCaptcha">
        INSERT INTO sys_image_captcha ( id, code)
        VALUES
            (#{id, jdbcType=VARCHAR}, #{captcha, jdbcType=VARCHAR})
    </insert>

    <!--查询指定ID的验证码是否失效-->
    <select id="checkImageCaptcha" resultType="int">
        SELECT
            count( 1 )
        FROM
            sys_image_captcha
        WHERE
            id = #{random, jdbcType=VARCHAR}
            AND state = 1
    </select>

    <!--失效图形验证码-->
    <update id="cancelImageCaptcha">
        UPDATE sys_image_captcha
        SET state = 0
        WHERE
            id = #{random, jdbcType=VARCHAR}
    </update>

    <!--查询指定账号最近一次登录成功且未退出的记录-->
    <select id="getLatestSuccessLog" resultType="hashmap">
        SELECT
            token
        FROM
            ( SELECT token FROM uac.sys_login_log WHERE account = #{account, jdbcType=VARCHAR} AND success = 1 AND logout_time IS NULL ORDER BY login_time DESC ) t
            FETCH FIRST 1 ROW ONLY
    </select>

    <!--检查登录日志的中token是否已失效-->
    <select id="checkTokenStatus" resultType="int">
        SELECT
            logout_type
        FROM
            uac.sys_login_log
        WHERE
            token = #{_parameter, jdbcType=VARCHAR}
            AND logout_time IS NOT NULL
            FETCH FIRST 1 ROW ONLY
    </select>

    <!--根据账号检查是否存在有效的短信验证码-->
    <select id="getSmsCaptcha" resultType="string">
    SELECT CAPTCHA
      FROM SYS_SMS_CAPTCHA
     WHERE ROUND(TO_NUMBER(EXPIRE_TIME - SYSDATE) * 1440) &gt; 0
       AND ACCOUNT = #{account, jdbcType=VARCHAR}
       AND PHONE = #{phone, jdbcType=VARCHAR}
       AND STATE = 1
       FETCH FIRST 1 ROW ONLY
    </select>

    <!--保存并发送短信验证码-->
    <insert id="sendSmsCaptcha">
      <if test="flag == 0">
      INSERT INTO SYS_SMS_CAPTCHA
        (ACCOUNT, PHONE, CAPTCHA, CREATE_TIME, EXPIRE_TIME)
      VALUES
        (#{account, jdbcType=VARCHAR},
         #{phone, jdbcType=VARCHAR},
         #{captcha, jdbcType=VARCHAR},
         SYSDATE,
         SYSDATE + 10 / 24 / 60);
      </if>

      INSERT INTO DM.DM_SMS_MONITOR
        (ID,
         DEVICE_NUMBER,
         CONTENT,
         TOPIC,
         PERSON,
         BUSI_TYPE,
         SP_NUMBER,
         SP_CODE,
         INSERT_TIME)
      VALUES
        (DM.SEQ_DM_SMS_MONITOR.NEXTVAL,
         #{phone, jdbcType=VARCHAR},
         '你的验证码为: ' || #{captcha, jdbcType=VARCHAR} || ', 十分钟内有效. 本条短信为系统自动发送, 请勿回复. 若非本人操作，请立即修改密码.',
         'css_pc_verify_code',
         'css',
         'css_sms',
         '*********',
         NULL,
         SYSDATE);
    </insert>

    <!--检查客户端录入的验证码是否正确且有效-->
    <select id="checkSmsCaptcha" resultType="int">
    SELECT COUNT(1)
      FROM SYS_SMS_CAPTCHA
     WHERE ROUND(TO_NUMBER(EXPIRE_TIME - SYSDATE) * 1440) &gt; 0
       AND ACCOUNT = #{account, jdbcType=VARCHAR}
       AND PHONE = #{phone, jdbcType=VARCHAR}
       AND CAPTCHA = #{captcha, jdbcType=VARCHAR}
       AND STATE = 1
    </select>

    <!--手动失效验证码-->
    <update id="invalidCaptcha">
        UPDATE SYS_SMS_CAPTCHA
           SET STATE = 0
         WHERE ACCOUNT = #{account, jdbcType=VARCHAR}
    </update>

    <!--重置密码(登录页忘记密码使用)-->
    <update id="resetPassword">
        UPDATE uac.sys_user
           SET password        = #{password, jdbcType=VARCHAR},
               password_state  = 1,
               password_expire = SYSDATE + '${expire}',
               salt            = #{salt, jdbcType=VARCHAR},
               memo            = '用户于' || to_char(SYSDATE,'yyyy-mm-dd hh24:mi:ss') || '重置了密码'
         WHERE account = #{account, jdbcType=VARCHAR}
    </update>

    <!--修改密码-->
    <update id="modifyPassword">
        UPDATE uac.sys_user
           SET update_user     = #{__OPERATOR, jdbcType=VARCHAR},
               password        = #{password, jdbcType=VARCHAR},
               password_state  = 1,
               password_expire = SYSDATE + #{expire, jdbcType=NUMERIC},
               salt            = #{salt, jdbcType=VARCHAR},
               memo            = '用户于' || TO_CHAR(SYSDATE,'yyyy-mm-dd hh24:mi:ss') || '修改了密码'
         WHERE account = #{account, jdbcType=VARCHAR}
    </update>

    <!--更新新加密策略下的密码-->
    <update id="updateNewPassword">
        UPDATE uac.sys_user
        SET password2 = #{password, jdbcType=VARCHAR}
        WHERE
            account = #{account, jdbcType=VARCHAR}
    </update>
</mapper>
