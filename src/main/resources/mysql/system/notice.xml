<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.notice">

    <resultMap id="listResultMap" type="hashmap">
        <collection property="regions" javaType="arraylist" column="{id=id}" select="getRegions" />
    </resultMap>
    <!--公告列表-->
    <select id="getList" resultMap="listResultMap">
        SELECT
            a.id "id",
            a.title "title",
            a.content "content",
            a.abstract "abstract",
            a.state "state",
            a.top "top",
            DATE_FORMAT( create_time, '%Y-%m-%d %H:%i:%s' ) "createTime",
            b.name "createUser",
            CASE
                WHEN c.notice_id IS NULL THEN
                0 ELSE 1
            END "read"
        FROM
            sys_notices a
            LEFT JOIN ( SELECT id, name FROM uac.sys_user ) b ON a.create_user = b.id
            LEFT JOIN ( SELECT DISTINCT notice_id FROM sys_notice_read WHERE state = 1 AND user_id = #{__OPERATOR, jdbcType=VARCHAR} ) c ON a.id = c.notice_id
        WHERE
            1 = 1
            <if test="title != null and title != ''">
            AND instr( a.title, #{title, jdbcType=VARCHAR} ) != 0
            </if>
            <if test="(state != null and state != '') or state == 0">
            AND a.state = #{state, jdbcType=NUMERIC}
            </if>
            <if test="startDate != null and startDate != ''">
            AND DATE_FORMAT( create_time, '%Y-%m-%d' ) >= #{startDate, jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
            AND DATE_FORMAT( create_time, '%Y-%m-%d' ) &lt;= #{endDate, jdbcType=VARCHAR}
            </if>
            <if test="id != null and id != ''">
                AND A.ID = #{id, jdbcType=VARCHAR}
            </if>
        ORDER BY
            a.create_time DESC,
            a.id DESC
    </select>

    <!--未读数量-->
    <select id="getUnreadCount" resultType="int">
        SELECT
            count( 1 )
        FROM
            sys_notices a
            LEFT JOIN ( SELECT DISTINCT notice_id FROM sys_notice_read WHERE state = 1 AND user_id = #{__OPERATOR, jdbcType=VARCHAR} ) b ON a.id = b.notice_id
        WHERE
            a.state = 1
    </select>

    <!--公告范围-->
    <select id="getRegions" resultType="string">
        SELECT
            DISTINCT region_id
        FROM
            sys_notice_region
        WHERE
            notice_id = #{id, jdbcType=VARCHAR}
    </select>

    <!--新增公告-->
    <insert id="add">
        INSERT INTO sys_notices ( id, title, abstract, content, state, top, create_user )
        VALUES
            (
                #{id, jdbcType=VARCHAR},
                #{title, jdbcType=VARCHAR},
                #{abstract, jdbcType=VARCHAR},
                #{content, jdbcType=VARCHAR},
                #{state, jdbcType=NUMERIC},
                #{top, jdbcType=NUMERIC},
                #{__OPERATOR, jdbcType=VARCHAR}
            );
        <if test="regions != null and regions.size() != 0">
            <foreach collection="regions" item="i">
                INSERT INTO sys_notice_region ( notice_id, region_id )
                VALUES
                    (#{id, jdbcType=VARCHAR},
                    #{i, jdbcType=VARCHAR}
                );
            </foreach>
        </if>
    </insert>

    <!--更新公告-->
    <update id="modify">
        UPDATE sys_notices
        SET title = #{title, jdbcType=VARCHAR},
            abstract = #{abstract, jdbcType=VARCHAR},
            content = #{content, jdbcType=VARCHAR},
            state = #{state, jdbcType=NUMERIC},
            top = #{top, jdbcType=NUMERIC}
        WHERE
            id = #{id, jdbcType=VARCHAR};
        DELETE
        FROM
            sys_notice_region
        WHERE
            notice_id = #{id, jdbcType=VARCHAR};
        <if test="regions != null and regions.size() != 0">
            <foreach collection="regions" item="i">
                INSERT INTO sys_notice_region ( notice_id, region_id )
                VALUES
                    (#{id, jdbcType=VARCHAR},
                    #{i, jdbcType=VARCHAR}
                );
            </foreach>
        </if>
    </update>

    <!--删除公告-->
    <delete id="delete">
        <choose>
            <when test="data != null and data.size() != 0">
                <foreach collection="data" item="i">
                    DELETE
                    FROM
                        sys_notices
                    WHERE
                        id = #{i, jdbcType=VARCHAR};
                    DELETE
                    FROM
                        sys_notice_region
                    WHERE
                        notice_id = #{i, jdbcType=VARCHAR};
                    DELETE
                    FROM
                        sys_notice_read
                    WHERE
                        notice_id = #{i, jdbcType=VARCHAR};
                </foreach>
            </when>
            <otherwise>
                SELECT 1
            </otherwise>
        </choose>
    </delete>

    <!--更新公告状态-->
    <update id="updateState">
        update sys_notices
           set state = #{state, jdbcType=NUMERIC}
         WHERE 1 = 1
         <choose>
             <when test="data != null and data.size() != 0">
                 AND id IN <foreach collection="data" item="i" open="(" close=")" separator=",">#{i, jdbcType=VARCHAR}</foreach>
             </when>
             <otherwise>AND 1 = 2</otherwise>
         </choose>
    </update>

    <!--查询是否有已读数据-->
    <select id="getReadCount" resultType="int">
        SELECT
            count( 1 )
        FROM
            sys_notice_read
        WHERE
            user_id = #{__OPERATOR, jdbcType=VARCHAR}
            AND notice_id = #{id, jdbcType=VARCHAR}
    </select>

    <!--更新为已读-->
    <update id="updateRead">
        UPDATE sys_notice_read
        SET state = 1
        WHERE
            user_id = #{__OPERATOR, jdbcType=VARCHAR}
            AND notice_id = #{id, jdbcType=VARCHAR}
    </update>

    <!--更新为已读或不提醒-->
    <insert id="insertRead">
        INSERT INTO sys_notice_read ( user_id, notice_id, state )
        VALUES
            (
                #{__OPERATOR, jdbcType=VARCHAR},
                #{id, jdbcType=VARCHAR},
                #{state, jdbcType=NUMERIC}
            )
    </insert>

    <!--批量更新为已读-->
    <update id="batchUpdateRead">
        INSERT INTO sys_notice_read ( user_id, notice_id, state ) SELECT DISTINCT
        #{__OPERATOR, jdbcType=VARCHAR},
        a.id,
        1
        FROM
            sys_notices a
            LEFT JOIN ( SELECT id, name FROM uac.sys_user ) b ON a.create_user = b.id
            LEFT JOIN ( SELECT DISTINCT notice_id FROM sys_notice_read WHERE user_id = #{__OPERATOR, jdbcType=VARCHAR} ) c ON a.id = c.notice_id
        WHERE
            a.state = 1;
        UPDATE sys_notice_read
        SET state = 1
        WHERE
            user_id = #{__OPERATOR, jdbcType=VARCHAR};
    </update>
</mapper>
