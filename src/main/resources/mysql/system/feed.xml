<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.feed">

    <!-- 查询组织结构层级 -->
    <sql id="orgNodeSql">
        SELECT ID
        FROM (select ID, PARENT_ID
              from DM.ORG_ORGANIZATION_MOD_ACCT
              WHERE month_id = SUBSTR(#{orgDate, jdbcType=VARCHAR}, 1, 6)
                AND day_id = SUBSTR(#{orgDate, jdbcType=VARCHAR}, 7, 2))
        START WITH ID = #{__ORG_ID, jdbcType=VARCHAR}
        CONNECT BY PRIOR ID = PARENT_ID
    </sql>

    <!-- 意见反馈查询公共字段 -->
    <sql id="feedSelectFields">
        select t.ID                                            as ID,
               t.LOGIN_ID                                      as SUGGESTER_ACCOUNT,
               t.ROLE_NAME                                     as SUGGESTER_NAME,
               t.ROLE_NUMBER                                   as SUGGESTER_NUMBER,
               t.TITLE_TYPE                                    as FEED_TITLE,
               t.TITLE_CONTENT                                 as FEED_CONTENT,
               to_char(t.TITLE_TIME, 'yyyy-MM-dd hh24:mi:ss')  as FEED_TIME,
               t.HANDLER_ID                                    as HANDLER_ID,
               t.HANDLER_NAME                                  as HANDLER_NAME,
               t.HANDLER_OPINION                               as HANDLER_OPINION,
               to_char(t.HANDLE_TIME, 'yyyy-MM-dd hh24:mi:ss') as HANDLE_TIME,
               t.HANDLE_STATUS                                 as HANDLE_STATUS,
               t2.ORG_ID                                       as ORG_ID,
               t3.NAME                                         as ORG_NAME,
               decode(t3.AREACODE, '087', 'root', t3.AREACODE) as ORG_AREACODE,
               decode(t4.NAME, null, '甘肃', t4.NAME)          as ORG_AREANAME
        from IOSS.USER_BOARD_SUGGESTION_BOX t
                 left join UAC.SYS_USER t1 on t.LOGIN_ID = t1.ACCOUNT
                 left join UAC.SYS_USER_ORG t2 on t1.ID = t2.USER_ID
                 left join (select *
                            from DM.ORG_ORGANIZATION_MOD_ACCT
                            where MONTH_ID = SUBSTR(#{orgDate, jdbcType=VARCHAR}, 1, 6)
                              and DAY_ID = SUBSTR(#{orgDate, jdbcType=VARCHAR}, 7, 2)) t3
                           on t2.ORG_ID = t3.ID
                 left join (select *
                            from DM.ORG_ORGANIZATION_MOD_ACCT
                            where MONTH_ID = SUBSTR(#{orgDate, jdbcType=VARCHAR}, 1, 6)
                              and DAY_ID = SUBSTR(#{orgDate, jdbcType=VARCHAR}, 7, 2)) t4
                           on t3.AREACODE = t4.ID
        where (t.DEL_STATUS IS NULL OR t.DEL_STATUS = 0)
    </sql>

    <!-- 意见反馈列表查询 -->
    <select id="getList" resultType="hashmap">
        <include refid="feedSelectFields"/>
        <!-- 提出人姓名 -->
        <if test="suggesterName != null and suggesterName != ''">
            AND INSTR(t.ROLE_NAME, #{suggesterName, jdbcType=VARCHAR}) > 0
        </if>
        <!-- 意见状态 -->
        <if test="handleStatus != null and handleStatus != ''">
            AND t.HANDLE_STATUS = #{handleStatus, jdbcType=NUMERIC}
        </if>
        <!-- 开始时间 -->
        <if test="startTime != null and startTime != ''">
            AND t.TITLE_TIME >= TO_DATE(#{startTime, jdbcType=VARCHAR}, 'YYYY-MM-DD')
        </if>
        <!-- 结束时间 -->
        <if test="endTime != null and endTime != ''">
            AND t.TITLE_TIME &lt;= TO_DATE(#{endTime, jdbcType=VARCHAR}, 'YYYY-MM-DD') + 1
        </if>
        <!-- 提出人所在地市 -->
        <if test="orgAreacode != null and orgAreacode != '' and orgAreacode != 'root'">
            AND t3.AREACODE = #{orgAreacode, jdbcType=VARCHAR}
        </if>
        <!-- 组织权限控制 -->
        <if test="__ORG_ID != 'root'">
            AND EXISTS (
            SELECT 1
            FROM (
            <include refid="orgNodeSql"/>
            ) nodes
            WHERE nodes.ID = t2.ORG_ID
            )
        </if>
        ORDER BY t.TITLE_TIME DESC
    </select>

    <!-- 根据ID查询意见反馈详情 -->
    <select id="getDetail" resultType="hashmap">
        <include refid="feedSelectFields"/>
        AND t.ID = #{id, jdbcType=VARCHAR}
    </select>

    <!-- 处理意见反馈 -->
    <update id="handle">
        UPDATE IOSS.USER_BOARD_SUGGESTION_BOX
        SET HANDLE_STATUS = #{handleStatus, jdbcType=NUMERIC},
            HANDLER_ID = #{handlerId, jdbcType=VARCHAR},
            HANDLER_NAME = #{handlerName, jdbcType=VARCHAR},
            HANDLER_OPINION = #{handlerOpinion, jdbcType=VARCHAR},
            HANDLE_TIME = SYSDATE
        WHERE ID = #{id, jdbcType=VARCHAR}
    </update>


    <!-- 删除意见反馈（逻辑删除） -->
    <update id="delete">
        UPDATE IOSS.USER_BOARD_SUGGESTION_BOX
        SET DEL_STATUS = 1
        WHERE ID = #{id, jdbcType=VARCHAR}
    </update>

</mapper>
