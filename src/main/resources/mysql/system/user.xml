<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.user">
    <resultMap id="listResultMap" type="hashmap">
        <collection property="roles" javaType="arraylist" column="{id=id}" select="getRoles"/>
        <collection property="positions" javaType="arraylist" column="{id=id}" select="getPositions"/>
    </resultMap>
    <!-- 查询组织结构层级 -->
    <sql id="orgNodeSql">
        SELECT ID
        FROM (select ID, PARENT_ID
              from DM.ORG_ORGANIZATION_MOD_ACCT
              WHERE month_id = SUBSTR(#{orgDate, jdbcType=VARCHAR}, 1, 6)
                AND day_id = SUBSTR(#{orgDate, jdbcType=VARCHAR}, 7, 2))
        START WITH ID = #{__ORG_ID, jdbcType=VARCHAR}
        CONNECT BY PRIOR ID = PARENT_ID
    </sql>

    <!-- 新增公用查询用户sql -->
    <sql id="baseSql">
        SELECT a.id           "id",
               a.account      "account",
               a.name         "name",
               a.gender       "gender",
               CASE
                   WHEN a.gender = 1 THEN
                       '男'
                   WHEN a.gender = 2 THEN
                       '女'
                   ELSE '未知'
                   END        "genderName",
               a.phone        "phone",
               a.email        "email",
               a.dept_id      "deptId",
               d.name         "deptName",
               c.id           "orgId",
               c.name         "orgName",
               c.orgrank_name "orgRankName",
               a.state        "state",
               CASE
                   WHEN a.state = 1 THEN
                       '启用中'
                   ELSE '已停用'
                   END        "stateName",
               a.is_admin     "isAdmin",
               a.memo         "memo"
        FROM uac.sys_user a
                 left JOIN uac.sys_user_org b on a.id = b.user_id
                 LEFT JOIN (SELECT ID, NAME, ORGRANK_NAME
                            FROM DM.ORG_ORGANIZATION_MOD_ACCT
                            WHERE month_id = SUBSTR(#{orgDate, jdbcType=VARCHAR}, 1, 6)
                              and day_id = SUBSTR(#{orgDate, jdbcType=VARCHAR}, 7, 2)) c ON b.org_id = c.id
                 LEFT JOIN (SELECT id, name FROM uac.sys_code_department) d ON a.dept_id = d.id
        WHERE 1 = 1
        <if test="account != null and account != ''">
            AND instr(a.account, #{account, jdbcType=VARCHAR}) &gt; 0
        </if>
        <if test="name != null and name != ''">
            AND instr(a.name, #{name, jdbcType=VARCHAR}) &gt; 0
        </if>
        <if test="orgId != null and orgId != ''">
            AND a.org_id = #{orgId, jdbcType=VARCHAR}
        </if>
        <if test="phone!= null and phone!= ''">
            AND instr(a.phone, #{phone, jdbcType=VARCHAR}) &gt; 0
        </if>
        <choose>
            <when test="special == 1 or special == '1'">
                AND (a.account IN ('yundx', 'yub7', 'lidl150', 'liangy66') OR a.dept_id = #{deptId, jdbcType=VARCHAR})
            </when>
            <otherwise>
                <if test="deptId != null and deptId != ''">
                    AND a.dept_id = #{deptId, jdbcType=VARCHAR}
                </if>
            </otherwise>
        </choose>
        <if test="departments != null and departments != ''">
            AND INSTR(#{departments, jdbcType=VARCHAR}, a.dept_id) != 0
        </if>
        <if test="(state != null and state != '') or state == 0">
            AND a.state = #{state, jdbcType=NUMERIC}
        </if>
        <if test="id != null and id != ''">
            AND a.id = #{id, jdbcType=VARCHAR}
        </if>
        <if test="__IS_ADMIN == 0 and all != 1">
            AND a.create_user = #{__OPERATOR, jdbcType=VARCHAR}
        </if>
        <!-- 查询所有 组织结构在orgNodeSql的人员  -->
        <if test="__ORG_ID != 'root'">
            <!--AND a.org_id in (
            <include refid="orgNodeSql"/>
            )-->

            AND EXISTS (
            SELECT 1
            FROM (
            <include refid="orgNodeSql"/>
            ) o
            WHERE o.ID = a.org_id
            )
        </if>
        ORDER BY a.create_time DESC nulls last, a.name, a.state DESC, a.id
    </sql>


    <!--用户列表-->
    <select id="getList" resultMap="listResultMap">
        <include refid="baseSql"/>
    </select>

    <!-- 导出用户查询（不需要关联角色和职位） -->
    <select id="getExportUserList" resultType="hashmap">
        <include refid="baseSql"/>
    </select>


    <!--用户角色-->
    <select id="getRoles" resultType="hashmap">
        SELECT a.id   "id",
               a.name "name"
        FROM ioss.sys_roles a
                 JOIN ioss.sys_user_role b ON a.id = b.role_id
        WHERE a.state = 1
          AND b.user_id = #{id, jdbcType=VARCHAR}
        ORDER BY a.odr
    </select>

    <!--用户职位-->
    <select id="getPositions" resultType="hashmap">
        SELECT b.id   "id",
               b.name "name"
        FROM uac.sys_user_position a
                 JOIN uac.sys_code_position b ON a.position_id = b.id
        WHERE b.state = 1
          AND a.user_id = #{id, jdbcType=VARCHAR}
        ORDER BY b.odr
    </select>

    <!--新增用户-->
    <insert id="add">
        INSERT INTO uac.sys_user (id, account, name, password, password_expire, salt, gender, phone, email, dept_id,
                                  org_id, state, memo, create_user)
        VALUES (#{id, jdbcType=VARCHAR},
                #{account, jdbcType=VARCHAR},
                #{name, jdbcType=VARCHAR},
                #{password, jdbcType=VARCHAR},
                SYSDATE + #{expire, jdbcType=NUMERIC},
                #{salt, jdbcType=VARCHAR},
                #{gender, jdbcType=VARCHAR},
                #{phone, jdbcType=VARCHAR},
                #{email, jdbcType=VARCHAR},
                #{deptId, jdbcType=VARCHAR},
                #{orgId, jdbcType=VARCHAR},
                #{state, jdbcType=NUMERIC},
                #{memo, jdbcType=VARCHAR},
                #{__OPERATOR, jdbcType=VARCHAR});

        <!-- 同步向 UAC.SYS_USER_ORG 表插入 user_id 和 org_id -->
        INSERT INTO uac.sys_user_org (user_id, org_id)
        VALUES (#{id, jdbcType=VARCHAR}, #{orgId, jdbcType=VARCHAR});

        <if test="positions != null and positions.size() != 0">
            <foreach collection="positions" item="i">
                INSERT INTO uac.sys_user_position (user_id, position_id, create_user)
                VALUES (#{id, jdbcType=VARCHAR},
                        #{i, jdbcType=VARCHAR},
                        #{__OPERATOR, jdbcType=VARCHAR});
            </foreach>
        </if>
    </insert>

    <!--更新用户-->
    <update id="modify">
        UPDATE uac.sys_user
        SET name = #{name, jdbcType=VARCHAR},
        <if test="password != null and password != ''">
            password        = #{password, jdbcType=VARCHAR},
            password_expire = SYSDATE + #{expire, jdbcType=NUMERIC},
            salt            = #{salt, jdbcType=VARCHAR},
        </if>
        gender      = #{gender, jdbcType=NUMERIC},
        phone       = #{phone, jdbcType=VARCHAR},
        email       = #{email, jdbcType=VARCHAR},
        dept_id     = #{deptId, jdbcType=VARCHAR},
        org_id      = #{orgId, jdbcType=VARCHAR},
        state       = #{state, jdbcType=NUMERIC},
        memo        = #{memo, jdbcType=VARCHAR},
        update_user = #{__OPERATOR, jdbcType=VARCHAR},
        update_time = systimestamp
        WHERE id = #{id, jdbcType=VARCHAR};
        <!-- 同步更新 UAC.SYS_USER_ORG -->
        UPDATE uac.sys_user_org
        SET org_id = #{orgId, jdbcType=VARCHAR}
        WHERE user_id = #{id, jdbcType=VARCHAR};
    </update>

    <!--删除用户信息-->
    <delete id="delete">
        <choose>
            <when test="data != null and data.size() != 0">
                <foreach collection="data" item="i">
                    DELETE
                    FROM uac.sys_user
                    WHERE account != 'admin'
                      AND id = #{i, jdbcType=VARCHAR};
                    DELETE
                    FROM ioss.sys_user_role
                    WHERE user_id = #{i, jdbcType=VARCHAR};
                    DELETE
                    FROM uac.sys_user_position
                    WHERE user_id = #{i, jdbcType=VARCHAR};
                    DELETE
                    FROM ioss.sys_user_resource
                    WHERE user_id = #{i, jdbcType=VARCHAR};
                    <!-- 同步删除UAC.SYS_USER_ORG -->
                    DELETE
                    FROM uac.sys_user_org
                    WHERE user_id = #{i, jdbcType=VARCHAR};
                </foreach>
            </when>
            <otherwise>
                SELECT 1
                from dual
            </otherwise>
        </choose>
    </delete>

    <!--更新用户状态-->
    <update id="updateState">
        update uac.sys_user
        set state = DECODE(#{operate, jdbcType=VARCHAR}, 'enable', 1, 0)
        WHERE account != 'admin'
        <choose>
            <when test="data != null and data.size() != 0">
                AND id IN
                <foreach collection="data" item="i" open="(" close=")" separator=",">
                    #{i, jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </update>

    <!--重置密码-->
    <update id="resetPassword">
        UPDATE uac.sys_user
        SET password        = #{password, jdbcType=VARCHAR},
            salt            = #{salt, jdbcType=VARCHAR},
            password_expire = SYSDATE + #{expire, jdbcType=NUMERIC},
            update_user     = #{__OPERATOR, jdbcType=VARCHAR},
            update_time     = systimestamp,
            memo            = '由' || #{__OPERATOR, jdbcType=VARCHAR} || '于' ||
                              TO_CHAR(SYSDATE, 'yyyy-mm-dd hh24:mi:ss') || '重置了密码'
        WHERE id = #{id, jdbcType=VARCHAR}
    </update>

    <!--校验字段值是否可用-->
    <select id="validField" resultType="int">
        SELECT COUNT(1)
        FROM uac.sys_user
        WHERE 1 = 1
        <if test="field == 'account'">
            AND account = #{value, jdbcType=VARCHAR}
        </if>
        <if test="field == 'phone'">
            AND phone = #{value, jdbcType=VARCHAR}
        </if>
        <if test="field == 'email'">
            AND email = #{value, jdbcType=VARCHAR}
        </if>
        <if test="id != null and id != ''">
            AND id != #{id, jdbcType=VARCHAR}
        </if>
    </select>

    <!--查询用户可授权资源-->
    <select id="getResources" resultType="hashmap">
        SELECT pid                         "pId",
               id                          "id",
               name                        "name",
               DECODE(leaf, 1, checked, 0) "checked",
               leaf                        "leaf",
               disabled                    "disabled",
               platform                    "platform"
        FROM
        ( SELECT t1.pid,
                 t1.id,
                 t1.name,
                 t1.platform,
                 CASE
                     WHEN t2.resource_id IS NOT NULL THEN
                         1
                     ELSE 0
                     END                  checked,
                 CASE
                     WHEN EXISTS (SELECT 1 FROM ioss.sys_resources WHERE pid = t1.id) THEN
                         0
                     ELSE 1
                     END                  leaf,
                 COALESCE(t2.disabled, 0) disabled,
                 t1.odr
          FROM ioss.sys_resources t1
                   LEFT JOIN (SELECT resource_id,
                                     min(disabled) disabled
                              FROM (SELECT user_id,
                                           resource_id,
                                           0 disabled
                                    FROM ioss.sys_user_resource
                                    WHERE checked = 'ALL'
                                    UNION ALL
                                    SELECT a.user_id,
                                           c.resource_id,
                                           1
                                    FROM ioss.sys_user_role a
                                             JOIN ioss.sys_roles b ON a.role_id = b.id
                                             JOIN ioss.sys_role_resource c ON a.role_id = c.role_id
                                    WHERE b.state = 1
                                      AND c.checked = 'ALL') t
                              WHERE user_id = #{id, jdbcType=VARCHAR}
                              GROUP BY resource_id) t2 ON t1.id = t2.resource_id
                   LEFT JOIN (SELECT DISTINCT resource_id
                              FROM (SELECT resource_id, user_id
                                    FROM ioss.sys_user_resource
                                    UNION ALL
                                    SELECT c.resource_id, a.user_id
                                    FROM ioss.sys_user_role a
                                             JOIN ioss.sys_roles b ON a.role_id = b.id
                                             JOIN ioss.sys_role_resource c ON a.role_id = c.role_id
                                    WHERE b.state = 1) t
                              WHERE user_id = #{__OPERATOR, jdbcType=VARCHAR}
                              GROUP BY resource_id) t3 ON t1.id = t3.resource_id
        WHERE 1 = 1
        <if test="__IS_ADMIN != 1">
            AND t3.resource_id IS NOT NULL
        </if>) t
    ORDER BY odr
    </select>

    <!--保存角色授权-->
    <update id="saveRoles">
        DELETE
        FROM ioss.sys_user_role
        WHERE user_id = #{id, jdbcType=VARCHAR};
        <if test="data != null and data.size() != 0">
            <foreach collection="data" item="i">
                INSERT INTO ioss.sys_user_role (user_id, role_id, create_user)
                VALUES (#{id, jdbcType=VARCHAR},
                        #{i, jdbcType=VARCHAR},
                        #{__OPERATOR, jdbcType=VARCHAR});
            </foreach>
        </if>
    </update>

    <!--保存资源授权-->
    <update id="saveResources">
        DELETE
        FROM ioss.sys_user_resource a
        WHERE a.user_id = #{id, jdbcType=VARCHAR}
          AND EXISTS (SELECT 1
                      FROM ioss.sys_resources b
                      WHERE a.resource_id = b.id
                        AND b.platform = #{platform, jdbcType=VARCHAR});
        <!--DELETE a
        FROM
            ioss.sys_user_resource a
            JOIN ioss.sys_resources b ON a.resource_id = b.id
        WHERE
            user_id = #{id, jdbcType=VARCHAR}
            AND b.platform = #{platform, jdbcType=VARCHAR};-->
        <if test="data != null and data.size() != 0">
            <foreach collection="data" item="i">
                INSERT INTO ioss.sys_user_resource (user_id, resource_id, checked, create_user)
                VALUES (#{id, jdbcType=VARCHAR}, #{i.id, jdbcType=VARCHAR}, #{i.checked, jdbcType=VARCHAR},
                        #{__OPERATOR, jdbcType=VARCHAR});
            </foreach>
        </if>
    </update>
</mapper>
