<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.code">

    <!--获得市县列表信息-->
    <select id="getRegions" resultType="hashmap">
        <!-- 使用递归 CTE 查询指定节点及其子节点 -->
        WITH region_tree(pId, id, label, type, hqId, fullName, odr) AS (
        <!-- 初始查询：找根节点（id 等于 __ORG_ID） -->
        SELECT pid       "pId",
               id        "id",
               name      "label",
               type      "type",
               hq_id     "hqId",
               full_name "fullName",
               odr       "odr"
        FROM uac.sys_code_region
        WHERE id = #{__ORG_ID, jdbcType=VARCHAR}
        UNION ALL
        <!--递归查询，找到子节点-->
        SELECT r.pid       AS pId,
               r.id        AS id,
               r.name      AS label,
               r.type      AS type,
               r.hq_id     AS hqId,
               r.full_name AS fullName,
               r.odr       AS odr
        FROM uac.sys_code_region r
                 JOIN
             region_tree rt ON r.pid = rt.id
        WHERE r.type  <![CDATA[<]]> 5
        )
        SELECT pId      AS "pId",
               id       AS "id",
               label    AS "label",
               type     AS "type",
               hqId     AS "hqId",
               fullName AS "fullName",
               odr      AS "odr"
        FROM region_tree
        WHERE 1 = 1
        <if test="value != null and value != ''">
            <!-- 使用 INSTR 函数在 label 列进行模糊匹配，Oracle 中列名无需反引号 -->
            AND INSTR(label, #{value, jdbcType=VARCHAR}) > 0
        </if>
        ORDER BY odr
    </select>

    <!-- 获取组织结构 -->
    <select id="getRegionsNew" resultType="hashmap">
        SELECT
        pid "pId",
        id "id",
        name "label",
        type "type",
        hq_id "hqId",
        full_name "fullName"
        FROM
        uac.sys_code_region
        WHERE
        1 = 1
        <if test="value != null and value != ''">
            AND instr(name, #{value, jdbcType=VARCHAR} ) > 0
        </if>
        <if test="__ORG_ID != '-1'">
            AND(
            pid = #{__ORG_ID, jdbcType=VARCHAR}
            OR id = #{__ORG_ID, jdbcType=VARCHAR}
            )
        </if>
        ORDER BY
        odr
    </select>


    <!--获取 收入归属渠道-->
    <select id="getIncomeChannels" resultType="hashmap">
        SELECT
            INCOME_CHANNEL_TYPE "id",
            INCOME_CHANNEL_TYPE_DESC  "label"
        FROM
            IDS_DM.DIM_INCOME_CHANNEL_TYPE
        ORDER BY
            INCOME_CHANNEL_TYPE
    </select>

    <!--获取 队伍-->
    <select id="getTeam" resultType="hashmap">
        SELECT
            TEAM_TYPE "id",
            TEAM_TYPE_DESC  "label"
        FROM
            IDS_DM.DIM_PUB_TEAM_TYPE
           WHERE IS_VALID = 1
           and TEAM_TYPE_DESC != '合计'
        ORDER BY
            IDX_NO
    </select>

    <!--获得部门列表信息-->
    <select id="getDepartments" resultType="hashmap">
        SELECT
            id "value",
            name "label"
        FROM
            uac.sys_code_department
        WHERE
            state = 1
            <if test="value != null and value != ''">
            AND instr( name, #{value, jdbcType=VARCHAR} ) > 0
            </if>
        ORDER BY
            odr
    </select>

    <!--获得职位列表信息-->
    <select id="getPositions" resultType="hashmap">
        SELECT
            id "value",
            name "label"
        FROM
            uac.sys_code_position
        WHERE
            state = 1
            <if test="value != null and value != ''">
            AND instr( name, #{value, jdbcType=VARCHAR} ) > 0
            </if>
        ORDER BY
            odr
    </select>


     <!--获得地市码表信息-->
    <select id="getCitys" resultType="hashmap">
        SELECT id   "value",
               name "label"
        FROM uac.sys_code_region
        WHERE 1 = 1
          and (pid = 'root' or id = 'root')
        order by odr
    </select>

      <!--获得投诉问题类型码表-->
    <select id="getTsTypes" resultType="hashmap">
       SELECT
           id "value",
		   id "label"
        FROM
            sys_code_complaint_question
		order by odr
    </select>

      <!--获取资源可视化码表-->
    <select id="getResourceCode" resultType="hashmap">
     SELECT  CODE "value" ,CODE_TYPE "type", CODE_DESC "label"  from IDS_DM.dim_resource_code
    </select>

</mapper>
