<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.resource">
    <!-- 数据列表 -->
    <select id="getList" resultType="hashmap">
        SELECT
            pid "pId",
            id "id",
            name "name",
            icon "icon",
            path "path",
            type "type",
            show "show",
            odr "odr",
            state "state",
            CASE
                WHEN EXISTS ( SELECT 1 FROM ioss.sys_resources WHERE pid = a.id ) THEN
                0 ELSE 1
            END "leaf",
            memo "memo",
            need_token "needToken",
            demand_dept "department",
            demand_user "demander",
            online_date "onlineDate",
            watermark "watermark",
            breadcrumb "breadcrumb",
            platform "platform",
            is_frame "isFrame",
            open_type "openType",
            auth_code "authCode",
            demand_code "demandCode",
            data_code "dataCode"
        FROM
            ( SELECT
                pid,
                id,
                name,
                icon,
                path,
                type,
                show,
                odr,
                state,
                memo,
                need_token,
                demand_dept,
                demand_user,
                online_date,
                watermark,
                breadcrumb,
                platform,
                is_frame,
                open_type,
                auth_code,
                demand_code,
                data_code
            FROM
                ioss.sys_resources
            WHERE
                platform = #{platform, jdbcType=VARCHAR} UNION ALL
            SELECT
                '',
                'root',
                '系统资源',
                '',
                '/',
                1,
                1,
                1,
                1,
                '',
                1,
                '',
                '',
                '',
                null,
                null,
                'IDS,APP',
                null,
                null,
                null,
                null,
                null from dual) a
         WHERE 1 = 1
           <if test="name != null and name != ''">
           AND INSTR(name, #{name, jdbcType=VARCHAR}) != 0
           </if>
           <if test="type != null and type != ''">
           AND type = #{type, jdbcType=NUMERIC}
           </if>
           <if test="platform != null and platform != ''">
               AND INSTR(platform, #{platform, jdbcType=NUMERIC}) > 0
           </if>
        ORDER BY odr
    </select>

    <!--新增资源-->
    <insert id="add">
        INSERT INTO ioss.sys_resources (
            pid,
            id,
            name,
            type,
            icon,
            path,
            show,
            odr,
            platform,
            watermark,
            breadcrumb,
            is_frame,
            open_type,
            auth_code,
            need_token,
            create_user,
            demand_dept,
            demand_user,
            online_date,
            demand_code,
            data_code
        )
        VALUES
        (
            #{pId, jdbcType=VARCHAR},
            #{id, jdbcType=VARCHAR},
            #{name, jdbcType=VARCHAR},
            #{type, jdbcType=NUMERIC},
            #{icon, jdbcType=VARCHAR},
            #{path, jdbcType=VARCHAR},
            #{show, jdbcType=NUMERIC},
            #{odr, jdbcType=NUMERIC},
            #{platform, jdbcType=VARCHAR},
            #{watermark, jdbcType=NUMERIC},
            #{breadcrumb, jdbcType=NUMERIC},
            #{isFrame, jdbcType=NUMERIC},
            #{openType, jdbcType=VARCHAR},
            #{authCode, jdbcType=VARCHAR},
            #{needToken, jdbcType=NUMERIC},
            #{__OPERATOR, jdbcType=VARCHAR},
            #{department, jdbcType=VARCHAR},
            #{demander, jdbcType=VARCHAR},
            #{onlineDate, jdbcType=VARCHAR},
            #{demandCode, jdbcType=VARCHAR},
            #{dataCode, jdbcType=VARCHAR}
        );
    </insert>

    <!--更新资源-->
    <update id="modify">
        UPDATE ioss.sys_resources
        SET name = #{name, jdbcType=VARCHAR},
            type = #{type, jdbcType=NUMERIC},
            icon = #{icon, jdbcType=VARCHAR},
            path = #{path, jdbcType=VARCHAR},
            show = #{show, jdbcType=NUMERIC},
            odr = #{odr, jdbcType=NUMERIC},
            platform = #{platform, jdbcType=VARCHAR},
            watermark = #{watermark, jdbcType=NUMERIC},
            breadcrumb = #{breadcrumb, jdbcType=NUMERIC},
            is_frame = #{isFrame, jdbcType=NUMERIC},
            open_type = #{openType, jdbcType=VARCHAR},
            auth_code = #{authCode, jdbcType=VARCHAR},
            need_token = #{needToken, jdbcType=NUMERIC},
            update_user = #{__OPERATOR, jdbcType=VARCHAR},
            demand_dept = #{department, jdbcType=VARCHAR},
            demand_user = #{demander, jdbcType=VARCHAR},
            online_date = #{onlineDate, jdbcType=VARCHAR},
            demand_code = #{demandCode, jdbcType=VARCHAR},
            data_code = #{dataCode, jdbcType=VARCHAR},
            pid = #{pId, jdbcType=VARCHAR}
        WHERE
	        id = #{id, jdbcType=VARCHAR}
    </update>

    <!-- 删除资源 -->
    <delete id="delete">
        DELETE FROM ioss.sys_role_resource
        WHERE resource_id = #{id, jdbcType=VARCHAR};
        DELETE FROM ioss.sys_user_resource
        WHERE resource_id = #{id, jdbcType=VARCHAR};
        DELETE FROM ioss.sys_resources WHERE id = #{id, jdbcType=VARCHAR};
    </delete>

    <!-- 更新排序、父级节点、状态等 -->
    <update id="update">
        <choose>
            <when test="operate != null and operate == 'sort'">
            UPDATE ioss.sys_resources
             SET pid = #{pId, jdbcType=VARCHAR}, odr = #{odr, jdbcType=NUMERIC}
            WHERE id = #{id, jdbcType=VARCHAR};
            </when>
            <when test="operate != null and operate == 'offline'">
            UPDATE ioss.sys_resources
             SET state = 0,
                 memo  = #{memo, jdbcType=VARCHAR}
            WHERE id = #{id, jdbcType=VARCHAR};
            </when>
            <when test="operate != null and operate == 'online'">
            UPDATE ioss.sys_resources
             SET state = 1,
                 memo  = ''
            WHERE id = #{id, jdbcType=VARCHAR};
            </when>
            <otherwise>
                SELECT 1
            </otherwise>
        </choose>
    </update>

    <!--校验权限标识是否可用-->
    <select id="checkValid" resultType="int">
        SELECT
            count( 1 )
        FROM
            ioss.sys_resources
        WHERE
            auth_code = #{authCode, jdbcType=VARCHAR}
            <if test="id != null and id != ''">
            AND id != #{id, jdbcType=VARCHAR}
            </if>
    </select>
</mapper>
