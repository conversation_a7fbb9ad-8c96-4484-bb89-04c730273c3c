<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.role">

    <!--列表数据-->
    <select id="getList" resultType="hashmap">
        SELECT
            pid "pId",
            id "id",
            name "name",
            auth_code "authCode",
            memo "memo",
            state "state",
            odr "odr",
            create_user "createUser",
            CASE
                WHEN EXISTS ( SELECT 1 FROM ioss.sys_roles WHERE pid = a.id ) THEN
                0 ELSE 1
            END "leaf"
        FROM
            ioss.sys_roles a
         WHERE 1 = 1
           <if test="name != null and name != ''">
           AND INSTR(name, #{name, jdbcType=VARCHAR}) &gt; 0
           </if>
           <if test="state != null and state != ''">
           AND state = #{state, jdbcType=NUMERIC}
           </if>
           <if test="id != null and id != ''">
               AND id = #{id, jdbcType=VARCHAR}
           </if>
           <!--非超级管理员只能查询自身拥有角色的下级角色-->
           <if test="__IS_ADMIN != 1">
               <choose>
                   <when test="__ROLES != null and __ROLES.size() != 0">
                       AND pid in <foreach collection="__ROLES" item="i" open="(" close=")" separator=",">#{i, jdbcType=VARCHAR}</foreach>
                   </when>
                   <otherwise>
                       AND 1 = 2
                   </otherwise>
               </choose>
           </if>
         ORDER BY odr, id
    </select>

    <!--查询权限标识查询是否存在-->
    <select id="checkAuthCode" resultType="int">
        SELECT
            count( 1 )
        FROM
            ioss.sys_roles
        WHERE
            auth_code = #{authCode, jdbcType=VARCHAR}
            <if test="id != null and id != ''">
                AND id != #{id, jdbcType=VARCHAR}
            </if>
    </select>

    <!--根据子节点查询父节点状态-->
    <select id="getParentState" resultType="int">
        SELECT
            NVL( sum( a.state ), 0 )
        FROM
            ioss.sys_roles a
            JOIN ioss.sys_roles b ON a.id = b.pid
        WHERE
            b.id = #{id, jdbcType=VARCHAR}
    </select>

    <!--新增角色-->
    <insert id="add">
        INSERT INTO ioss.sys_roles
          (pid, id, name, auth_code, memo, odr, create_user)
        VALUES
          (#{pId, jdbcType=VARCHAR},
           #{id, jdbcType=VARCHAR},
           #{name, jdbcType=VARCHAR},
           #{authCode, jdbcType=VARCHAR},
           #{memo, jdbcType=VARCHAR},
           #{odr, jdbcType=NUMERIC},
           #{__OPERATOR, jdbcType=VARCHAR})
    </insert>

    <!--更新角色-->
    <update id="modify">
        UPDATE ioss.sys_roles
           SET name        = #{name, jdbcType=VARCHAR},
               auth_code     = #{authCode, jdbcType=VARCHAR},
               memo          = #{memo, jdbcType=VARCHAR},
               odr           = #{odr, jdbcType=NUMERIC},
               update_user   = #{__OPERATOR, jdbcType=VARCHAR}
         WHERE id = #{id, jdbcType=VARCHAR}
    </update>

    <!--禁用或启用（禁用操作会将父子都禁用，启用只是启用当前操作的）-->
    <update id="forbid">
        UPDATE ioss.sys_roles
           SET state = #{state, jdbcType=NUMERIC},
               update_user = #{__OPERATOR, jdbcType=VARCHAR}
         WHERE id = #{id, jdbcType=VARCHAR} <if test="state != 1">OR pid = #{id, jdbcType=VARCHAR}</if>
    </update>

    <!--删除-->
    <delete id="delete">
        <choose>
            <when test="data != null and data.size() != 0">
                DELETE FROM ioss.sys_user_role WHERE role_id IN <foreach collection="data" open="(" close=")" separator="," item="i">#{i, jdbcType=VARCHAR}</foreach>;
                DELETE FROM ioss.sys_role_resource WHERE role_id IN <foreach collection="data" open="(" close=")" separator="," item="i">#{i, jdbcType=VARCHAR}</foreach>;
                DELETE FROM ioss.sys_roles WHERE id IN <foreach collection="data" open="(" close=")" separator="," item="i">#{i, jdbcType=VARCHAR}</foreach>;
            </when>
            <otherwise>SELECT 1 from dual;</otherwise>
        </choose>
    </delete>

    <!--可授权或回收的用户-->
    <select id="getUsers" resultType="hashmap">
        SELECT
            a.id "id",
            account "account",
            a.name "name"
        FROM
            uac.sys_user a
            LEFT JOIN (
            SELECT DISTINCT
                user_id
            FROM
                ioss.sys_user_role
            WHERE
                role_id = #{roleId, jdbcType=VARCHAR}) b
                ON a.id = b.user_id
            WHERE
                1 = 1
            <if test="option == 'grant'">
               AND b.user_id IS NULL
               AND a.state = 1
            </if>
            <if test="option == 'revoke'">
               AND b.user_id IS NOT NULL
            </if>
            <if test="__IS_ADMIN == 0">
                AND a.create_user = #{__OPERATOR, jdbcType=VARCHAR}
            </if>
            <if test="account != null and account != ''">
                AND INSTR(a.account, #{account, jdbcType=VARCHAR}) != 0
            </if>
            ORDER BY
            create_time DESC,
            a.id
    </select>

    <!--保存或删除授权用户-->
    <!--<update id="saveUsers" statementType="CALLABLE">-->
    <update id="saveUsers">
        <choose>
          <when test="option == 'grant'">
              <foreach collection="data" item="i">
                  INSERT INTO ioss.sys_user_role ( role_id, user_id, create_user )
                  VALUES
                    ( #{roleId, jdbcType=VARCHAR}, #{i, jdbcType=VARCHAR}, #{__OPERATOR, jdbcType=VARCHAR} );
              </foreach>
          </when>
          <otherwise>
              DELETE FROM ioss.sys_user_role
               WHERE role_id = #{roleId, jdbcType=VARCHAR}
                 AND user_id IN <foreach collection="data" item="i" separator="," open="(" close=")">#{i, jdbcType=VARCHAR}</foreach>;
          </otherwise>
        </choose>
    </update>

    <!--可授权的资源-->
    <select id="getResources" resultType="hashmap">
        SELECT
            pid "pId",
            id "id",
            name "name",
            DECODE ( leaf, 1, checked, 0) "checked",
            leaf "leaf",
            disabled "disabled",
            platform "platform"
        FROM (
        SELECT
            a.*,
            CASE
                WHEN b.resource_id IS NOT NULL THEN
                1 ELSE 0
            END checked,
            CASE
                WHEN EXISTS ( SELECT 1 FROM ioss.sys_resources WHERE pid = a.id ) THEN
                0 ELSE 1
            END leaf
        FROM
            ( SELECT pid, id, name, state, odr, 0 disabled, platform, type FROM ioss.sys_resources ) a
            LEFT JOIN ( SELECT resource_id FROM ioss.sys_role_resource WHERE checked = 'ALL' AND role_id = #{roleId, jdbcType=VARCHAR} ) b ON a.id = b.resource_id
            LEFT JOIN (
            SELECT
                DISTINCT resource_id
            FROM
                ( SELECT resource_id, user_id FROM ioss.sys_user_resource WHERE checked = 'ALL' UNION ALL SELECT c.resource_id, a.user_id FROM ioss.sys_user_role a JOIN ioss.sys_roles b ON a.role_id = b.id JOIN ioss.sys_role_resource c ON a.role_id = c.role_id WHERE b.state = 1 AND c.checked = 'ALL' ) t
            WHERE
                user_id = #{__OPERATOR, jdbcType=VARCHAR}
            GROUP BY
                resource_id
            ) c ON a.id = c.resource_id
        WHERE
            a.state = 1
        <if test="__IS_ADMIN == 0">
            AND c.resource_id IS NOT NULL
        </if>
        ) t
        ORDER BY
            odr
    </select>

    <!--保存授权的资源-->
    <!--<update id="saveResources" statementType="CALLABLE">-->
    <update id="saveResources">
        DELETE FROM ioss.sys_role_resource a
        WHERE a.role_id = #{roleId, jdbcType=VARCHAR}
        AND EXISTS (
            SELECT 1  FROM ioss.sys_resources b
            WHERE a.resource_id = b.id
            AND b.platform = #{platform, jdbcType=VARCHAR}
        );
        <!--DELETE a
        FROM
            ioss.sys_role_resource a
            JOIN ioss.sys_resources b ON a.resource_id = b.id
        WHERE
            a.role_id = #{roleId, jdbcType=VARCHAR}
            AND b.platform = #{platform, jdbcType=VARCHAR};-->
      <if test="data != null and data.size() != 0">
          <foreach collection="data" item="i">
              INSERT INTO ioss.sys_role_resource ( role_id, resource_id, checked, create_user )
              VALUES
                ( #{roleId, jdbcType=VARCHAR}, #{i.id, jdbcType=VARCHAR}, #{i.checked, jdbcType=VARCHAR}, #{__OPERATOR, jdbcType=VARCHAR} );
          </foreach>
      </if>
    </update>
</mapper>
