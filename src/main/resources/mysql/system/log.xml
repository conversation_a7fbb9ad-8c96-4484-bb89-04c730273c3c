<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.log">

    <!--记录登录日志-->
    <insert id="saveLoginLog">
        INSERT INTO uac.sys_login_log ( id, account, ip, browser, os, sign_type, token, success, memo, logout_time )
        VALUES
        (
            #{id, jdbcType=VARCHAR},
            #{account, jdbcType=VARCHAR},
            #{ip, jdbcType=VARCHAR},
            #{browser, jdbcType=VARCHAR},
            #{os, jdbcType=VARCHAR},
            NVL(#{signType, jdbcType=VARCHAR}, 'SYS'),
            #{token, jdbcType=VARCHAR},
            #{success, jdbcType=NUMERIC},
            #{memo, jdbcType=VARCHAR},
            #{logoutTime, jdbcType=VARCHAR}
        )
    </insert>

    <!--更新登录日志（退出时间）-->
    <update id="updateLogoutTime">
    UPDATE uac.sys_login_log
       SET logout_time = SYSDATE
     WHERE account = #{__ACCOUNT, jdbcType=VARCHAR}
       AND token = #{token, jdbcType=VARCHAR}
    </update>

    <!--登录日志-->
    <select id="getLoginList" resultType="hashmap">
        SELECT
            a.id "id",
            a.account "account",
            b.name "name",
            c.name "departmentName",
            d.name "orgName",
            a.ip "clientIp",
            date_format( a.login_time, '%Y-%m-%d %H:%i:%s' ) "loginTime",
            CASE
                WHEN UPPER(a.sign_type) = 'SYS' THEN
                '系统直登'
                WHEN UPPER(a.sign_type) = 'AI_PORTAL' THEN
                '智慧门户'
                WHEN UPPER(a.sign_type) = 'UNICOM_DSS_SSO' THEN
                '联通经分门户'
                WHEN UPPER(a.sign_type) = 'WISDOM_SSO' THEN
                '智慧大屏'
                WHEN UPPER(a.sign_type) = 'APP' THEN
                '经营决策APP'
                WHEN UPPER(a.sign_type) = 'LTBG' THEN
                '联通办公APP'
                WHEN UPPER(a.sign_type) = 'ZW' THEN
                '联通经分APP'
                WHEN UPPER(a.sign_type) = 'ZQ_SSO' THEN
                '政企运营平台' ELSE '其他'
            END "loginSource",
            a.memo "memo"
        FROM
            uac.sys_login_log a
            LEFT JOIN uac.sys_user b ON a.account = b.account
            LEFT JOIN uac.sys_code_department c ON b.dept_id = c.id
            LEFT JOIN uac.sys_code_region d ON b.org_id = d.id
        WHERE
            1 = 1
           <if test="account != null and account != ''">
           AND a.account = #{account, jdbcType=VARCHAR}
           </if>
           <if test="name != null and name != ''">
           AND INSTR(b.name, #{name, jdbcType=VARCHAR}) &gt; 0
           </if>
           <if test="startDate != null and startDate != ''">
           AND date_format(A.LOGIN_TIME, '%Y-%m-%d') &gt;= #{startDate, jdbcType=VARCHAR}
           </if>
           <if test="endDate != null and endDate != ''">
           AND date_format(A.LOGIN_TIME, '%Y-%m-%d') &lt;= #{endDate, jdbcType=VARCHAR}
           </if>
         ORDER BY a.login_time DESC, a.id
    </select>

    <!--访问日志-->
    <select id="getVisitList" resultType="hashmap">
        SELECT
            a.id "id",
            a.account "account",
            b.name "name",
            c.name "departmentName",
            d.name "orgName",
            a.page_name "page",
            date_format( a.visit_time, '%Y-%m-%d %H:%i:%s' ) "visitTime",
            CASE
                WHEN UPPER(a.client) = 'APP' THEN
                'APP'
                WHEN UPPER(a.client) = 'PC' THEN
                'PC' ELSE '其他'
            END "source",
            NVL(stay_duration, 0) "duration"
        FROM
            ioss.sys_visit_log a
            LEFT JOIN uac.sys_user b ON a.account = b.account
            LEFT JOIN uac.sys_code_department c ON b.dept_id = c.id
            LEFT JOIN uac.sys_code_region d ON b.org_id = d.id
        WHERE
            a.env = 'production'
           <if test="name != null and name != ''">
           AND (INSTR(b.name, #{name, jdbcType=VARCHAR}) &gt; 0 OR a.account = #{name, jdbcType=VARCHAR})
           </if>
           <if test="page != null and page != ''">
           AND INSTR(a.page_name, #{page, jdbcType=VARCHAR}) &gt; 0
           </if>
           <if test="startDate != null and startDate != ''">
           AND date_format(a.visit_time, '%Y-%m-%d') &gt;= #{startDate, jdbcType=VARCHAR}
           </if>
           <if test="endDate != null and endDate != ''">
           AND date_format(a.visit_time, '%Y-%m-%d') &lt;= #{endDate, jdbcType=VARCHAR}
           </if>
         ORDER BY a.visit_time DESC, a.id
    </select>

     <!--导入日志-->
    <select id="getUploadList" resultType="hashmap">
        SELECT
            a.id "id",
            a.import_user "import_user",
            b.name "name",
            c.name "departmentName",
            d.name "orgName",
            a.page_name "page",
            date_format( a.import_time, '%Y-%m-%d %H:%i:%s' ) "importTime",
            TABLE_DESC "tableDesc",
            TABLE_NAME "tableName",
            FILE_DATE "fileDate",
            TABLE_DESC "tableDesc",
            IMP_RESULT "impReSult",
            data_count "dataCount"
        FROM
            sys_import_log a
            LEFT JOIN uac.sys_user b ON a.import_user = b.account
            LEFT JOIN sys_code_department c ON b.dept_id = c.id
            LEFT JOIN sys_code_region d ON b.org_id = d.id
        WHERE
           1=1


            <if test="tableName != null and tableName != ''">
           AND INSTR(a.TABLE_NAME, #{tableName, jdbcType=VARCHAR}) &gt; 0
           </if>
           <if test="name != null and name != ''">
           AND (INSTR(b.name, #{name, jdbcType=VARCHAR}) &gt; 0 OR a.import_user = #{name, jdbcType=VARCHAR})
           </if>
           <if test="page != null and page != ''">
           AND INSTR(a.page_name, #{page, jdbcType=VARCHAR}) &gt; 0
           </if>
           <if test="startDate != null and startDate != ''">
           AND date_format(a.import_time, '%Y-%m-%d') &gt;= #{startDate, jdbcType=VARCHAR}
           </if>
           <if test="endDate != null and endDate != ''">
           AND date_format(a.import_time, '%Y-%m-%d') &lt;= #{endDate, jdbcType=VARCHAR}
           </if>
         ORDER BY a.import_time DESC, a.id
    </select>

    <!--访问排名-->
    <select id="getRankList" resultType="hashmap">
        SELECT
            a.page_path "path",
            page_name "page",
            count( 1 ) "count",
            b.account "account",
            user_name "name",
            date_format ( b.visit_time, '%Y-%m-%d %H:%i:%s' ) "visitTime"
        FROM
            ioss.sys_visit_log a
            LEFT JOIN (
            SELECT
                page_path,
                account,
                user_name,
                visit_time
            FROM
                (
                SELECT
                    a.*,
                    @rn :=
                    CASE
                        WHEN @page = page_path THEN
                        @rn + 1 ELSE 1
                    END AS rn,
                    @page := page_path AS page_path1
                FROM
                    (
                    SELECT
                        page_path,
                        b.account,
                        b.name user_name,
                        visit_time
                    FROM
                        ioss.sys_visit_log A
                        JOIN uac.sys_user b ON A.account = b.account
                    WHERE
                        A.env = 'production'
                        <if test="startDate != null and startDate != ''">
                        AND date_format( visit_time, '%Y-%m-%d' ) &gt;= #{startDate, jdbcType=VARCHAR}
                        </if>
                        <if test="endDate != null and endDate != ''">
                        AND date_format( visit_time, '%Y-%m-%d' ) &lt;= #{endDate, jdbcType=VARCHAR}
                        </if>
                    ORDER BY
                        a.page_path,
                        a.visit_time DESC
                    ) a,
                    ( SELECT @rn = 0, @page = 0 ) b
                ) t1
            WHERE
                rn = 1
            ) b ON a.page_path = b.page_path
        WHERE
            a.env = 'production'
        <if test="page != null and page != ''">
            AND INSTR(a.page_name, #{page, jdbcType=VARCHAR}) > 0
        </if>
        <if test="startDate != null and startDate != ''">
            AND date_format(a.visit_time, '%Y-%m-%d') &gt;= #{startDate, jdbcType=VARCHAR}
        </if>
        <if test="endDate != null and endDate != ''">
            AND date_format(a.visit_time, '%Y-%m-%d') &lt;= #{endDate, jdbcType=VARCHAR}
        </if>
        GROUP BY
            a.page_path,
            page_name,
            b.account,
            user_name,
            b.visit_time
        ORDER BY
            3 DESC,
            5 DESC,
            1
    </select>

    <!--数据导出日志-->
    <select id="getExportList" resultType="hashmap">
        SELECT
            a.id "id",
            page_name "page",
            page_path "path",
            export_count "count",
            date_format ( export_time, '%Y-%m-%d %H:%i:%s' ) "exportTime",
            a.account "account",
            b.name "name",
            c.name "departmentName",
            d.name "orgName"
        FROM
            ioss.sys_export_log a
            LEFT JOIN uac.sys_user b ON a.account = b.account
            LEFT JOIN uac.sys_code_department c ON b.dept_id = c.id
            LEFT JOIN uac.sys_code_region d ON b.org_id = d.id
        WHERE
            1 =1
        <if test="startDate != null and startDate != ''">
            AND date_format(a.export_time, '%Y-%m-%d') &gt;= #{startDate, jdbcType=VARCHAR}
        </if>
        <if test="endDate != null and endDate != ''">
            AND date_format(a.export_time, '%Y-%m-%d') &lt;= #{endDate, jdbcType=VARCHAR}
        </if>
       <if test="page != null and page != ''">
           AND INSTR(a.page_name, #{page, jdbcType=VARCHAR}) &gt; 0
       </if>
       <if test="name != null and name != ''">
           AND (INSTR(b.name, #{name, jdbcType=VARCHAR}) &gt; 0 OR a.account = #{account, jdbcType=VARCHAR})
       </if>
        ORDER BY a.export_time DESC, a.id
    </select>

    <!--新增通用操作日志-->
    <insert id="saveOperateLog">
        INSERT INTO ioss.sys_operate_log (
            id,
            title,
            business_type,
            func,
            method,
            url,
            ip,
            location,
            params,
            result,
            status,
            memo,
            cost_time,
            create_user
        )
        VALUES
            (
                #{id, jdbcType=VARCHAR},
                #{title, jdbcType=VARCHAR},
                #{businessType, jdbcType=VARCHAR},
                #{func, jdbcType=VARCHAR},
                #{method, jdbcType=VARCHAR},
                #{url, jdbcType=VARCHAR},
                #{ip, jdbcType=VARCHAR},
                #{location, jdbcType=VARCHAR},
                #{params, jdbcType=VARCHAR},
                #{result, jdbcType=VARCHAR},
                #{status, jdbcType=NUMERIC},
                #{memo, jdbcType=VARCHAR},
                #{costTime, jdbcType=NUMERIC},
                #{userId, jdbcType=VARCHAR}
        )
    </insert>

    <!--操作日志列表-->
    <select id="getOperateList" resultType="hashmap">
        SELECT
            a.id "id",
            a.title "title",
            a.business_type "businessType",
            b.account "account",
            a.func "func",
            a.method "method",
            a.url "url",
            a.ip "ip",
            a.location "location",
            a.params "reqBody",
            a.result "rspBody",
            a.status "status",
            a.memo "memo",
            a.cost_time "costTime",
            date_format( a.create_time, '%Y-%m-%d %H:%i:%s' ) "createTime",
            b.name "name",
            c.name "deptName",
            d.name "orgName"
        FROM
            ioss.sys_operate_log a
            LEFT JOIN uac.sys_user b ON a.create_user = b.id
            LEFT JOIN uac.sys_code_department c ON b.dept_id = c.id
            LEFT JOIN uac.sys_code_region d ON b.org_id = d.id
        WHERE
            1 = 1
           <if test="user != null and user != ''">
           AND (INSTR(b.name, #{user, jdbcType=VARCHAR}) &gt; 0 OR a.account = #{user, jdbcType=VARCHAR})
           </if>
           <if test="title != null and title != ''">
           AND INSTR(a.title, #{title, jdbcType=VARCHAR}) &gt; 0
           </if>
           <if test="businessType != null and businessType != ''">
           AND a.business_type = #{businessType, jdbcType=NUMERIC}
           </if>
           <if test="status != null and status != ''">
           AND a.status = #{status, jdbcType=NUMERIC}
           </if>
           <if test="startDate != null and startDate != ''">
           AND date_format(a.create_time, '%Y-%m-%d') &gt;= #{startDate, jdbcType=VARCHAR}
           </if>
           <if test="endDate != null and endDate != ''">
           AND date_format(a.create_time, '%Y-%m-%d') &lt;= #{endDate, jdbcType=VARCHAR}
           </if>
         ORDER BY a.create_time DESC, a.id DESC
    </select>

    <!--保存达梦数据共享配置操作日志-->
    <insert id="saveDamShareOptLog">
        INSERT sys_dam_share_operate_log ( id, type, prev_content, content, create_user )
        VALUES
        (
            #{id},
            #{type},
            #{prevContent},
            #{content},
            #{createUser}
        )
    </insert>

    <!--行云达梦数据共享配置操作日志列表-->
    <select id="getDamShareOptLogList" resultType="hashmap">
        SELECT
            id "id",
            type "type",
            prev_content "prevContent",
            content "content",
            t2.name "createUser",
            DATE_FORMAT( create_time, '%Y-%m-%d %H:%i:%s' ) "createTime"
        FROM
            sys_dam_share_operate_log t1
            LEFT JOIN ( SELECT account, name FROM uac.sys_user ) t2 ON t1.create_user = t2.account
        WHERE
            1 = 1
        <if test="type != null and type != ''">
            AND t1.type = #{type}
        </if>
        <if test="createUser != null and createUser != ''">
            AND ( instr( t1.create_user, #{createUser} ) > 0 OR instr( t2.name, #{createUser} ) > 0 )
        </if>
        <if test="jobId != null and jobId != ''">
            AND instr( t1.content, #{jobId} ) > 0
        </if>
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            AND DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) BETWEEN #{startDate} AND #{endDate}
        </if>
        ORDER BY
            create_time DESC,
            id DESC
    </select>

    <!--保存全客投产配置操作日志-->
    <insert id="saveQuanKeOptLog">
        INSERT sys_quanke_operate_log ( id, type, prev_content, content, create_user, audit_user )
        VALUES
        (
            #{id},
            #{type},
            #{prevContent},
            #{content},
            #{createUser},
            #{auditUser}
        )
    </insert>

    <!--全客投产配置操作日志列表-->
    <select id="getQuanKeOptLogList" resultType="hashmap">
        SELECT
            id "id",
            type "type",
            prev_content "prevContent",
            content "content",
            t2.name "createUser",
            t3.name "auditUser",
            DATE_FORMAT( create_time, '%Y-%m-%d %H:%i:%s' ) "createTime"
        FROM
            sys_quanke_operate_log t1
            LEFT JOIN ( SELECT account, name FROM uac.sys_user ) t2 ON t1.create_user = t2.account
            LEFT JOIN ( SELECT account, name FROM uac.sys_user ) t3 ON t1.audit_user = t3.account
        WHERE
            1 = 1
        <if test="type != null and type != ''">
            AND t1.type = #{type}
        </if>
        <if test="createUser != null and createUser != ''">
            AND ( instr( t1.create_user, #{createUser} ) > 0 OR instr( t2.name, #{createUser} ) > 0 )
        </if>
        <if test="taskId != null and taskId != ''">
            AND JSON_EXTRACT(t1.content, '$.id') = CAST(#{taskId} AS SIGNED)
        </if>
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            AND DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) BETWEEN #{startDate} AND #{endDate}
        </if>
        ORDER BY
            create_time DESC,
            id DESC
    </select>

</mapper>
