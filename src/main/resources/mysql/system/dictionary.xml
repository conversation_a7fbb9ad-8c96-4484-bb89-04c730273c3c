<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mysql.system.dictionary">

    <!--数据列表-->
    <select id="getList" resultType="hashmap">
        SELECT
            pid "pId",
            id "id",
            name "name",
            system "system",
            odr "odr",
            CASE
                WHEN EXISTS ( SELECT 1 FROM sys_dictionary WHERE state = 1 AND pid = a.id ) THEN
                0 ELSE 1
            END "leaf"
        FROM
            sys_dictionary a
        WHERE
            state = 1
            <if test="name != null and name != ''">
                AND INSTR(name, #{name}) != 0
            </if>
            <if test="pId != null and pId != ''">
                 AND FIND_IN_SET(pid, #{pId})
            </if>
        ORDER BY
            odr,
            id
    </select>

    <!--保存字典-->
    <insert id="insert">
        INSERT INTO sys_dictionary ( pid, id, name, odr, create_user )
        VALUES
        (
            #{pId},
            #{id},
            #{name},
            #{odr, jdbcType=NUMERIC},
            #{__OPERATOR}
        )
    </insert>

    <!--更新字典-->
    <update id="update">
        UPDATE sys_dictionary
        SET name = #{name},
        odr = #{odr, jdbcType=NUMERIC}
        WHERE
            id = #{id}
    </update>

    <!--删除字典-->
    <delete id="delete">
        UPDATE sys_dictionary SET state = 0 WHERE id = #{id}
    </delete>

    <!--检测字典编码是否存在-->
    <select id="valid" resultType="int">
        SELECT COUNT(1) FROM sys_dictionary WHERE id = #{id}
    </select>


</mapper>
