<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.bonc</groupId>
    <artifactId>bonc-ids-server-cloud</artifactId>
    <version>1.0-SNAPSHOT</version>
    <description>IDS智慧决策系统数据接口服务</description>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<project.resources.sourceEncoding>UTF-8</project.resources.sourceEncoding>
        <satoken.version>1.37.0</satoken.version>
        <hutool.version>5.8.20</hutool.version>
        <!-- 修复CVE-2025-24813漏洞：升级Tomcat到最新版本 -->
        <tomcat.version>9.0.107</tomcat.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
	    <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.2.0</version>
            <exclusions>
                <exclusion>
					<groupId>org.apache.tomcat</groupId>
					<artifactId>tomcat-jdbc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-devtools</artifactId>-->
<!--            <scope>runtime</scope>-->
<!--            <optional>true</optional>-->
<!--        </dependency>-->
	    <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>8.0.33</version>
            <scope>runtime</scope>
        </dependency>
        <!-- oceanbase驱动 -->
        <dependency>
            <groupId>com.oceanbase</groupId>
            <artifactId>oceanbase-client</artifactId>
            <version>2.4.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
	    <dependency>
		    <groupId>com.github.pagehelper</groupId>
		    <artifactId>pagehelper</artifactId>
		    <version>5.2.1</version>
		</dependency>
	    <dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
        <dependency>
		  	<groupId>org.apache.commons</groupId>
		  	<artifactId>commons-text</artifactId>
		  	<version>1.4</version>
		</dependency>
        <!--Excel操作工具-->
        <dependency>
		  	<groupId>com.github.liaochong</groupId>
		  	<artifactId>myexcel</artifactId>
		  	<version>4.0.1</version>
		</dependency>
	    <!--验证码工具-->
	    <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
            <version>1.6.2</version>
        </dependency>
        <!--访问COS依赖组件-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.628</version>
        </dependency>
        <!--JSON操作类-->
        <dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.7.0</version>
		</dependency>
        <dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-core</artifactId>
			<version>${hutool.version}</version>
		</dependency>
        <dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-jwt</artifactId>
			<version>${hutool.version}</version>
		</dependency>
        <dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-http</artifactId>
			<version>${hutool.version}</version>
		</dependency>
        <dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-extra</artifactId>
			<version>${hutool.version}</version>
		</dependency>
        <dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-crypto</artifactId>
			<version>${hutool.version}</version>
		</dependency>
        <dependency>
		   	<groupId>com.alibaba</groupId>
		   	<artifactId>druid-spring-boot-starter</artifactId>
		   	<version>1.2.19</version>
		</dependency>
        <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
            <version>${satoken.version}</version>
        </dependency>
        <!-- Sa-Token 整合 Redis （使用 jackson 序列化方式） -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-jackson</artifactId>
            <version>${satoken.version}</version>
        </dependency>
        <!-- Sa-Token 整合 jwt -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
            <version>${satoken.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.14.2</version>
        </dependency>
        <dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-spring</artifactId>
			<version>1.13.0</version>
		</dependency>
    </dependencies>

    <build>
	    <plugins>
	        <plugin>
		        <groupId>org.springframework.boot</groupId>
		        <artifactId>spring-boot-maven-plugin</artifactId>
		        <configuration>
		            <excludes>
			            <exclude>
			                <groupId>org.projectlombok</groupId>
			                <artifactId>lombok</artifactId>
			            </exclude>
		            </excludes>
			        <includeSystemScope>true</includeSystemScope>
		        </configuration>
	        </plugin>
	    </plugins>
    </build>


</project>
