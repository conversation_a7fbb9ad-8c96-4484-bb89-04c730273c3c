FROM harbor.dcos.guangzhou.unicom.local/common/openjdk:8

VOLUME /tmp

# 解决验证码字体问题
# RUN apk add --update ttf-dejavu && rm -rf /var/cache/apk/*

COPY ./target/bonc-ids-server-cloud-1.0-SNAPSHOT.jar ids-server.jar

EXPOSE 19001

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone

#ENTRYPOINT ["java", "-Xmx2688M", "-Xms2688M", "-Xmn960M", "-XX:MaxMetaspaceSize=256M", "-XX:MetaspaceSize=256M", "-jar", "/app.jar", "--spring.profiles.active=prod"]
ENTRYPOINT ["java", "-XX:+UnlockExperimentalVMOptions", "-XX:+UseCGroupMemoryLimitForHeap", "-XX:MaxRAMPercentage=70.0", "-XX:+HeapDumpOnOutOfMemoryError", "-XX:HeapDumpPath=/data/logs/", "-jar", "/app.jar", "--spring.profiles.active=prod"]