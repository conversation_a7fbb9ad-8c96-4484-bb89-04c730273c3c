apiVersion: apps/v1
kind: Deployment
metadata:
  name: bonc-ids-pc-server
  namespace: default
  labels:
    app: bonc-ids-pc-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bonc-ids-pc-server

  template:
    metadata:
      name: bonc-ids-pc-server
      labels:
        app: bonc-ids-pc-server

    spec:
      containers:
        - name: bonc-ids-pc-server
          image: harbor.dcos.xixian.unicom.local/ioss/ids-server:latest
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              memory: "1024Mi"
              cpu: "500m"
            limits:
              memory: "4096Mi"
              cpu: "1"
          # 在 Pod 定义的 command 和 args 中传入命令行参数。此方法最高优先级。
          command:
              - "java"
              - "-Dserver.port=8064"
              # 设置 Spring Boot 使用的配置文件
              - "-Dspring.profiles.active=prod"
              #  # 这是 Jasypt 加密的PWD
              # - "-Djasypt.encryptor.password=WXYAPP_PC"
              - "-Xms512m"
              - "-Xmx2g"
              - "-Xmn1g"
              - "-XX:MaxMetaspaceSize=256m"
              - "-XX:MetaspaceSize=256m"
              - "-XX:+UseG1GC"
              - "-XX:MaxGCPauseMillis=200"
              - "-jar"
              - "/opt/ids-server.jar"
          ports:
             - containerPort: 8064

      restartPolicy: Always
      # 配置host映射
#       hostAliases:
#         - ip: "*************"
#           hostnames:
#             - "jf.chinaunicom.cn"
      hostAliases:
        - ip: "127.0.0.1"
          hostnames:
          - "foo.local"
          - "bar.local"
        - ip: "********"
          hostnames:
          - "foo.remote"
          - "bar.remote"
