apiVersion: apps/v1
kind: Deployment
metadata:
  name: bonc-ids-pc-server
  namespace: default
  labels:
    app: bonc-ids-pc-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bonc-ids-pc-server

  template:
    metadata:
      name: bonc-ids-pc-server
      labels:
        app: bonc-ids-pc-server

    spec:
      containers:
        - name: bonc-ids-pc-server
          image: harbor.dcos.xixian.unicom.local/ioss-ts/ids-server:latest
          # imagePullPolicy: Always 每次 Pod 启动时，Kubernetes 不论节点上是否已有该镜像，都会尝试获取最新的镜像版本。适合开发环境。
          # imagePullPolicy: IfNotPresent 只有在节点上不存在镜像时才会拉取，适合大多数稳定的生产环境。 
          imagePullPolicy: Always
          # 在 Pod 定义的 command 和 args 中传入命令行参数。此方法最高优先级。
          command:
              - "java"
              - "-Dserver.port=8094"
              # 设置 Spring Boot 使用的配置文件
              - "-Dspring.profiles.active=dev"
              #  # 这是 Jasypt 加密的PWD
              # - "-Djasypt.encryptor.password=WXYAPP_PC"
#               - "-Xms1g"
#               - "-Xmx6g"
#               - "-Xmn2g"
#               - "-XX:MaxMetaspaceSize=512m"
#               - "-XX:MetaspaceSize=512m"
#               - "-XX:+UseG1GC"
#               - "-XX:MaxGCPauseMillis=200"
              - "-jar"
              - "/opt/ids-server.jar"
          ports:
             - containerPort: 8094

      restartPolicy: Always
      # 配置host映射 ARM区需要配置
#       hostAliases:
#           - ip: "*************"
#             hostnames:
#               - "jf.unicom.local"
#       hostAliases:
#         - ip: "127.0.0.1"
#           hostnames:
#           - "foo.local"
#           - "bar.local"
#         - ip: "********"
#           hostnames:
#           - "foo.remote"
#           - "bar.remote"
